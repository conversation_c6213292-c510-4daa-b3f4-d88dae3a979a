$version Generated by VerilatedVcd $end
$date Wed Aug 11 12:35:42 2021 $end
$timescale 1ps $end

 $scope module top $end
  $var wire  1 T" clk $end
  $scope module t $end
   $var wire 32 U" DEPTH [31:0] $end
   $var wire 32 W" NUMSUB [31:0] $end
   $var wire 32 V" WIDTH [31:0] $end
   $var wire  1 T" clk $end
   $var wire 64 Y" expc [63:0] $end
   $var wire  8 X" in [7:0] $end
   $var wire  8 w! out[0] [7:0] $end
   $var wire  8 x! out[1] [7:0] $end
   $var wire  8 y! out[2] [7:0] $end
   $var wire  8 z! out[3] [7:0] $end
   $var wire  8 {! out[4] [7:0] $end
   $var wire  8 |! out[5] [7:0] $end
   $var wire  8 }! out[6] [7:0] $end
   $var wire  8 ~! out[7] [7:0] $end
   $var wire  8 !" out[8] [7:0] $end
   $var wire  3 - shift [2:0] $end
   $var wire  8 #" through_tmp [7:0] $end
   $scope module always_block $end
    $var wire  1 . failed $end
    $scope module unnamedblk1 $end
     $var wire 32 / i [31:0] $end
    $upscope $end
   $upscope $end
   $scope module delay0 $end
    $var wire 32 t! c [31:0] $end
    $var wire  1 T" clk $end
    $var wire  1 y" unpack_sig0(10) $end
    $var wire  1 z" unpack_sig0(11) $end
    $var wire  1 {" unpack_sig0(12) $end
    $var wire  1 d! unpack_sig0(13) $end
    $var wire  1 e! unpack_sig0(14) $end
    $var wire  1 f! unpack_sig0(15) $end
    $var wire  1 g! unpack_sig0(16) $end
    $var wire  1 h! unpack_sig1(13) $end
    $var wire  1 i! unpack_sig1(14) $end
    $var wire  1 j! unpack_sig1(15) $end
    $var wire  1 k! unpack_sig1(16) $end
    $var wire  1 |" unpack_sig2(10) $end
    $var wire  1 }" unpack_sig2(11) $end
    $var wire  1 ~" unpack_sig2(12) $end
    $var wire  1 l! unpack_sig2(13) $end
    $var wire  1 m! unpack_sig2(14) $end
    $var wire  1 n! unpack_sig2(15) $end
    $var wire  1 o! unpack_sig2(16) $end
    $var wire  1 p! unpack_sig3(13) $end
    $var wire  1 q! unpack_sig3(14) $end
    $var wire  1 r! unpack_sig3(15) $end
    $var wire  1 s! unpack_sig3(16) $end
   $upscope $end
   $scope module i_t_array_rev $end
    $var wire  1 ' arrd(0) $end
    $var wire  1 ( arrd(1) $end
    $var wire  1 T" clk $end
    $var wire 32 !# cyc [31:0] $end
    $var wire  1 u! localbkw(0) $end
    $var wire  1 v! localbkw(1) $end
    $var wire  1 ) y0 $end
    $var wire  1 * y1 $end
    $scope module arr_rev_u $end
     $var wire  1 + arrbkw[0] $end
     $var wire  1 , arrbkw[1] $end
     $var wire  1 ) y0 $end
     $var wire  1 * y1 $end
    $upscope $end
   $upscope $end
   $scope module i_var_decl_with_init $end
    $var wire 32 # var0 [-1:30] $end
    $var wire 32 % var1 [30:-1] $end
    $var wire 32 $ var2 [-1:30] $end
    $var wire 32 & var3 [30:-1] $end
   $upscope $end
   $scope module shifter0 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 [" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 0 out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire  8 2 tmp(-1) [7:0] $end
    $var wire  8 1 tmp(-2) [7:0] $end
    $var wire  8 X" tmp(-3) [7:0] $end
    $var wire  8 0 tmp(0) [7:0] $end
   $upscope $end
   $scope module shifter1 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 [" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 3 out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire  8 4 tmp(-1) [7:0] $end
    $var wire  8 1 tmp(-2) [7:0] $end
    $var wire  8 X" tmp(-3) [7:0] $end
    $var wire  8 3 tmp(0) [7:0] $end
   $upscope $end
   $scope module shifter2 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 \" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 5 out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire  8 X" tmp(1) [7:0] $end
    $var wire  8 6 tmp(2) [7:0] $end
    $var wire  8 7 tmp(3) [7:0] $end
    $var wire  8 5 tmp(4) [7:0] $end
   $upscope $end
   $scope module shifter3 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 U" N [31:0] $end
    $var wire 32 \" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 $" out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire  8 X" tmp0(1)(1) [7:0] $end
    $var wire  8 X" tmp0(1)(2) [7:0] $end
    $var wire  8 X" tmp0(1)(3) [7:0] $end
    $var wire  8 1 tmp0(2)(1) [7:0] $end
    $var wire  8 1 tmp0(2)(2) [7:0] $end
    $var wire  8 1 tmp0(2)(3) [7:0] $end
    $var wire  8 8 tmp0(3)(1) [7:0] $end
    $var wire  8 9 tmp0(3)(2) [7:0] $end
    $var wire  8 : tmp0(3)(3) [7:0] $end
    $var wire  8 ; tmp0(4)(1) [7:0] $end
    $var wire  8 < tmp0(4)(2) [7:0] $end
    $var wire  8 = tmp0(4)(3) [7:0] $end
    $var wire  8 > tmp1(1)(1) [7:0] $end
    $var wire  8 ? tmp1(1)(2) [7:0] $end
    $var wire  8 @ tmp1(1)(3) [7:0] $end
    $var wire  8 A tmp1(2)(1) [7:0] $end
    $var wire  8 B tmp1(2)(2) [7:0] $end
    $var wire  8 C tmp1(2)(3) [7:0] $end
    $var wire  8 D tmp1(3)(1) [7:0] $end
    $var wire  8 E tmp1(3)(2) [7:0] $end
    $var wire  8 F tmp1(3)(3) [7:0] $end
    $var wire  8 G tmp1(4)(1) [7:0] $end
    $var wire  8 H tmp1(4)(2) [7:0] $end
    $var wire  8 I tmp1(4)(3) [7:0] $end
    $var wire  8 L! tmp10(1)(1) [7:0] $end
    $var wire  8 M! tmp10(1)(2) [7:0] $end
    $var wire  8 N! tmp10(1)(3) [7:0] $end
    $var wire  8 O! tmp10(2)(1) [7:0] $end
    $var wire  8 P! tmp10(2)(2) [7:0] $end
    $var wire  8 Q! tmp10(2)(3) [7:0] $end
    $var wire  8 R! tmp10(3)(1) [7:0] $end
    $var wire  8 S! tmp10(3)(2) [7:0] $end
    $var wire  8 T! tmp10(3)(3) [7:0] $end
    $var wire  8 U! tmp10(4)(1) [7:0] $end
    $var wire  8 V! tmp10(4)(2) [7:0] $end
    $var wire  8 W! tmp10(4)(3) [7:0] $end
    $var wire  8 %" tmp12(-1)(1)(1) [7:0] $end
    $var wire  8 &" tmp12(-1)(1)(2) [7:0] $end
    $var wire  8 '" tmp12(-1)(1)(3) [7:0] $end
    $var wire  8 (" tmp12(-1)(2)(1) [7:0] $end
    $var wire  8 )" tmp12(-1)(2)(2) [7:0] $end
    $var wire  8 *" tmp12(-1)(2)(3) [7:0] $end
    $var wire  8 +" tmp12(-1)(3)(1) [7:0] $end
    $var wire  8 ," tmp12(-1)(3)(2) [7:0] $end
    $var wire  8 -" tmp12(-1)(3)(3) [7:0] $end
    $var wire  8 $" tmp12(-1)(4)(1) [7:0] $end
    $var wire  8 ." tmp12(-1)(4)(2) [7:0] $end
    $var wire  8 /" tmp12(-1)(4)(3) [7:0] $end
    $var wire  8 0" tmp12(0)(1)(1) [7:0] $end
    $var wire  8 1" tmp12(0)(1)(2) [7:0] $end
    $var wire  8 2" tmp12(0)(1)(3) [7:0] $end
    $var wire  8 3" tmp12(0)(2)(1) [7:0] $end
    $var wire  8 4" tmp12(0)(2)(2) [7:0] $end
    $var wire  8 5" tmp12(0)(2)(3) [7:0] $end
    $var wire  8 6" tmp12(0)(3)(1) [7:0] $end
    $var wire  8 7" tmp12(0)(3)(2) [7:0] $end
    $var wire  8 8" tmp12(0)(3)(3) [7:0] $end
    $var wire  8 9" tmp12(0)(4)(1) [7:0] $end
    $var wire  8 :" tmp12(0)(4)(2) [7:0] $end
    $var wire  8 ;" tmp12(0)(4)(3) [7:0] $end
    $var wire  8 i" tmp13(1)(1) [7:0] $end
    $var wire  8 j" tmp13(1)(2) [7:0] $end
    $var wire  8 k" tmp13(1)(3) [7:0] $end
    $var wire  8 l" tmp13(2)(1) [7:0] $end
    $var wire  8 m" tmp13(2)(2) [7:0] $end
    $var wire  8 n" tmp13(2)(3) [7:0] $end
    $var wire  8 o" tmp13(3)(1) [7:0] $end
    $var wire  8 p" tmp13(3)(2) [7:0] $end
    $var wire  8 q" tmp13(3)(3) [7:0] $end
    $var wire  8 r" tmp13(4)(1) [7:0] $end
    $var wire  8 s" tmp13(4)(2) [7:0] $end
    $var wire  8 t" tmp13(4)(3) [7:0] $end
    $var wire  8 J tmp2[1][1] [7:0] $end
    $var wire  8 K tmp2[1][2] [7:0] $end
    $var wire  8 L tmp2[1][3] [7:0] $end
    $var wire  8 M tmp2[2][1] [7:0] $end
    $var wire  8 N tmp2[2][2] [7:0] $end
    $var wire  8 O tmp2[2][3] [7:0] $end
    $var wire  8 P tmp2[3][1] [7:0] $end
    $var wire  8 Q tmp2[3][2] [7:0] $end
    $var wire  8 R tmp2[3][3] [7:0] $end
    $var wire  8 S tmp2[4][1] [7:0] $end
    $var wire  8 T tmp2[4][2] [7:0] $end
    $var wire  8 U tmp2[4][3] [7:0] $end
    $var wire  8 V tmp3(1)(1) [7:0] $end
    $var wire  8 W tmp3(1)(2) [7:0] $end
    $var wire  8 X tmp3(1)(3) [7:0] $end
    $var wire  8 Y tmp3(2)(1) [7:0] $end
    $var wire  8 Z tmp3(2)(2) [7:0] $end
    $var wire  8 [ tmp3(2)(3) [7:0] $end
    $var wire  8 \ tmp3(3)(1) [7:0] $end
    $var wire  8 ] tmp3(3)(2) [7:0] $end
    $var wire  8 ^ tmp3(3)(3) [7:0] $end
    $var wire  8 _ tmp3(4)(1) [7:0] $end
    $var wire  8 ` tmp3(4)(2) [7:0] $end
    $var wire  8 a tmp3(4)(3) [7:0] $end
    $var wire  8 b tmp4(1)(1) [7:0] $end
    $var wire  8 c tmp4(1)(2) [7:0] $end
    $var wire  8 d tmp4(1)(3) [7:0] $end
    $var wire  8 e tmp4(2)(1) [7:0] $end
    $var wire  8 f tmp4(2)(2) [7:0] $end
    $var wire  8 g tmp4(2)(3) [7:0] $end
    $var wire  8 h tmp4(3)(1) [7:0] $end
    $var wire  8 i tmp4(3)(2) [7:0] $end
    $var wire  8 j tmp4(3)(3) [7:0] $end
    $var wire  8 k tmp4(4)(1) [7:0] $end
    $var wire  8 l tmp4(4)(2) [7:0] $end
    $var wire  8 m tmp4(4)(3) [7:0] $end
    $var wire  8 n tmp5[1][1] [7:0] $end
    $var wire  8 o tmp5[1][2] [7:0] $end
    $var wire  8 p tmp5[1][3] [7:0] $end
    $var wire  8 q tmp5[2][1] [7:0] $end
    $var wire  8 r tmp5[2][2] [7:0] $end
    $var wire  8 s tmp5[2][3] [7:0] $end
    $var wire  8 t tmp5[3][1] [7:0] $end
    $var wire  8 u tmp5[3][2] [7:0] $end
    $var wire  8 v tmp5[3][3] [7:0] $end
    $var wire  8 w tmp5[4][1] [7:0] $end
    $var wire  8 x tmp5[4][2] [7:0] $end
    $var wire  8 y tmp5[4][3] [7:0] $end
    $var wire  8 z tmp6(1)(1) [7:0] $end
    $var wire  8 { tmp6(1)(2) [7:0] $end
    $var wire  8 | tmp6(1)(3) [7:0] $end
    $var wire  8 } tmp6(2)(1) [7:0] $end
    $var wire  8 ~ tmp6(2)(2) [7:0] $end
    $var wire  8 !! tmp6(2)(3) [7:0] $end
    $var wire  8 "! tmp6(3)(1) [7:0] $end
    $var wire  8 #! tmp6(3)(2) [7:0] $end
    $var wire  8 $! tmp6(3)(3) [7:0] $end
    $var wire  8 %! tmp6(4)(1) [7:0] $end
    $var wire  8 &! tmp6(4)(2) [7:0] $end
    $var wire  8 '! tmp6(4)(3) [7:0] $end
    $var wire  8 (! tmp7(2)(1) [7:0] $end
    $var wire  8 )! tmp7(2)(2) [7:0] $end
    $var wire  8 *! tmp7(2)(3) [7:0] $end
    $var wire  8 +! tmp7(3)(1) [7:0] $end
    $var wire  8 ,! tmp7(3)(2) [7:0] $end
    $var wire  8 -! tmp7(3)(3) [7:0] $end
    $var wire  8 .! tmp7(4)(1) [7:0] $end
    $var wire  8 /! tmp7(4)(2) [7:0] $end
    $var wire  8 0! tmp7(4)(3) [7:0] $end
    $var wire  8 1! tmp7(5)(1) [7:0] $end
    $var wire  8 2! tmp7(5)(2) [7:0] $end
    $var wire  8 3! tmp7(5)(3) [7:0] $end
    $var wire  8 ]" tmp8(0)(1) [7:0] $end
    $var wire  8 ^" tmp8(0)(2) [7:0] $end
    $var wire  8 _" tmp8(0)(3) [7:0] $end
    $var wire  8 `" tmp8(1)(1) [7:0] $end
    $var wire  8 a" tmp8(1)(2) [7:0] $end
    $var wire  8 b" tmp8(1)(3) [7:0] $end
    $var wire  8 4! tmp8(2)(1) [7:0] $end
    $var wire  8 5! tmp8(2)(2) [7:0] $end
    $var wire  8 6! tmp8(2)(3) [7:0] $end
    $var wire  8 7! tmp8(3)(1) [7:0] $end
    $var wire  8 8! tmp8(3)(2) [7:0] $end
    $var wire  8 9! tmp8(3)(3) [7:0] $end
    $var wire  8 :! tmp8(4)(1) [7:0] $end
    $var wire  8 ;! tmp8(4)(2) [7:0] $end
    $var wire  8 <! tmp8(4)(3) [7:0] $end
    $var wire  8 =! tmp8(5)(1) [7:0] $end
    $var wire  8 >! tmp8(5)(2) [7:0] $end
    $var wire  8 ?! tmp8(5)(3) [7:0] $end
    $var wire  8 c" tmp8(6)(1) [7:0] $end
    $var wire  8 d" tmp8(6)(2) [7:0] $end
    $var wire  8 e" tmp8(6)(3) [7:0] $end
    $var wire  8 f" tmp8(7)(1) [7:0] $end
    $var wire  8 g" tmp8(7)(2) [7:0] $end
    $var wire  8 h" tmp8(7)(3) [7:0] $end
    $var wire  8 @! tmp9(4)(1) [7:0] $end
    $var wire  8 A! tmp9(4)(2) [7:0] $end
    $var wire  8 B! tmp9(4)(3) [7:0] $end
    $var wire  8 C! tmp9(5)(1) [7:0] $end
    $var wire  8 D! tmp9(5)(2) [7:0] $end
    $var wire  8 E! tmp9(5)(3) [7:0] $end
    $var wire  8 F! tmp9(6)(1) [7:0] $end
    $var wire  8 G! tmp9(6)(2) [7:0] $end
    $var wire  8 H! tmp9(6)(3) [7:0] $end
    $var wire  8 I! tmp9(7)(1) [7:0] $end
    $var wire  8 J! tmp9(7)(2) [7:0] $end
    $var wire  8 K! tmp9(7)(3) [7:0] $end
   $upscope $end
   $scope module shifter4 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 u" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 X! out [7:0] $end
    $var wire 24 w" pad [23:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire 32 v" tmp(2) [31:0] $end
    $var wire 32 Y! tmp(3) [31:0] $end
    $var wire 32 Z! tmp(4) [31:0] $end
    $var wire 32 [! tmp(5) [31:0] $end
   $upscope $end
   $scope module shifter5 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 x" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 \! out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire 32 ]! tmp [31:0] $end
   $upscope $end
   $scope module shifter6 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 x" OFFSET [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 ^! out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire 32 _! tmp [31:0] $end
   $upscope $end
   $scope module shifter7 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 `! out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire 32 a! tmp [31:0] $end
   $upscope $end
   $scope module shifter8 $end
    $var wire 32 U" DEPTH [31:0] $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 X" in [7:0] $end
    $var wire  8 b! out [7:0] $end
    $var wire  3 - shift [2:0] $end
    $var wire 32 c! tmp [0:31] $end
   $upscope $end
   $scope module though0 $end
    $var wire 32 V" WIDTH [31:0] $end
    $var wire  8 "" in [7:0] $end
    $var wire  8 #" out [7:0] $end
    $var wire  1 <" unpack_tmp(0) $end
    $var wire  1 =" unpack_tmp(1) $end
    $var wire  1 >" unpack_tmp(2) $end
    $var wire  1 ?" unpack_tmp(3) $end
    $var wire  1 @" unpack_tmp(4) $end
    $var wire  1 A" unpack_tmp(5) $end
    $var wire  1 B" unpack_tmp(6) $end
    $var wire  1 C" unpack_tmp(7) $end
    $scope module i_pack2unpack $end
     $var wire 32 V" WIDTH [31:0] $end
     $var wire  8 "" in [7:0] $end
     $var wire  1 D" out[0] $end
     $var wire  1 E" out[1] $end
     $var wire  1 F" out[2] $end
     $var wire  1 G" out[3] $end
     $var wire  1 H" out[4] $end
     $var wire  1 I" out[5] $end
     $var wire  1 J" out[6] $end
     $var wire  1 K" out[7] $end
    $upscope $end
    $scope module i_unpack2pack $end
     $var wire 32 V" WIDTH [31:0] $end
     $var wire  1 L" in[0] $end
     $var wire  1 M" in[1] $end
     $var wire  1 N" in[2] $end
     $var wire  1 O" in[3] $end
     $var wire  1 P" in[4] $end
     $var wire  1 Q" in[5] $end
     $var wire  1 R" in[6] $end
     $var wire  1 S" in[7] $end
     $var wire  8 #" out [7:0] $end
    $upscope $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000001001000110100010101100111 #
b00100000000000000000000000000000 $
b00000001001000110100000000100111 %
b00000000000000000000000000000011 &
1'
0(
0)
1*
0+
1,
b000 -
0.
b00000000000000000000000000000000 /
b10001110 0
b10001110 1
b10001110 2
b10001110 3
b10001110 4
b10001110 5
b10001110 6
b10001110 7
b10001110 8
b10001110 9
b10001110 :
b10001110 ;
b10001110 <
b10001110 =
b10001110 >
b10001110 ?
b10001110 @
b10001110 A
b10001110 B
b10001110 C
b10001110 D
b10001110 E
b10001110 F
b10001110 G
b10001110 H
b10001110 I
b10001110 J
b10001110 K
b10001110 L
b10001110 M
b10001110 N
b10001110 O
b10001110 P
b10001110 Q
b10001110 R
b10001110 S
b10001110 T
b10001110 U
b10001110 V
b10001110 W
b10001110 X
b10001110 Y
b10001110 Z
b10001110 [
b10001110 \
b10001110 ]
b10001110 ^
b10001110 _
b10001110 `
b10001110 a
b10001110 b
b10001110 c
b10001110 d
b10001110 e
b10001110 f
b10001110 g
b10001110 h
b10001110 i
b10001110 j
b10001110 k
b10001110 l
b10001110 m
b10001110 n
b10001110 o
b10001110 p
b10001110 q
b10001110 r
b10001110 s
b10001110 t
b10001110 u
b10001110 v
b10001110 w
b10001110 x
b10001110 y
b10001110 z
b10001110 {
b10001110 |
b10001110 }
b10001110 ~
b10001110 !!
b10001110 "!
b10001110 #!
b10001110 $!
b10001110 %!
b10001110 &!
b10001110 '!
b10001110 (!
b10001110 )!
b10001110 *!
b10001110 +!
b10001110 ,!
b10001110 -!
b10001110 .!
b10001110 /!
b10001110 0!
b10001110 1!
b10001110 2!
b10001110 3!
b10001110 4!
b10001110 5!
b10001110 6!
b10001110 7!
b10001110 8!
b10001110 9!
b10001110 :!
b10001110 ;!
b10001110 <!
b10001110 =!
b10001110 >!
b10001110 ?!
b10001110 @!
b10001110 A!
b10001110 B!
b10001110 C!
b10001110 D!
b10001110 E!
b10001110 F!
b10001110 G!
b10001110 H!
b10001110 I!
b10001110 J!
b10001110 K!
b10001110 L!
b10001110 M!
b10001110 N!
b10001110 O!
b10001110 P!
b10001110 Q!
b10001110 R!
b10001110 S!
b10001110 T!
b10001110 U!
b10001110 V!
b10001110 W!
b10001110 X!
b00000000000000000000000010001110 Y!
b00000000000000000000000010001110 Z!
b00000000000000000000000010001110 [!
b10001110 \!
b10001110100011101000111010001110 ]!
b10001110 ^!
b10001110100011101000111010001110 _!
b10001110 `!
b10001110100011101000111010001110 a!
b10001110 b!
b10001110100011101000111010001110 c!
0d!
0e!
0f!
0g!
0h!
0i!
0j!
0k!
0l!
0m!
0n!
0o!
0p!
0q!
0r!
0s!
b00000000000000000000000000000000 t!
0u!
0v!
b10001110 w!
b10001110 x!
b10001110 y!
b10001110 z!
b10001110 {!
b10001110 |!
b10001110 }!
b10001110 ~!
b10001110 !"
b10001110 ""
b10001110 #"
b10001110 $"
b10001110 %"
b10001110 &"
b10001110 '"
b10001110 ("
b10001110 )"
b10001110 *"
b10001110 +"
b10001110 ,"
b10001110 -"
b10001110 ."
b10001110 /"
b10001110 0"
b10001110 1"
b10001110 2"
b10001110 3"
b10001110 4"
b10001110 5"
b10001110 6"
b10001110 7"
b10001110 8"
b10001110 9"
b10001110 :"
b10001110 ;"
1<"
0="
0>"
0?"
1@"
1A"
1B"
0C"
0D"
1E"
1F"
1G"
0H"
0I"
0J"
1K"
0L"
1M"
1N"
1O"
0P"
0Q"
0R"
1S"
0T"
b00000000000000000000000000000011 U"
b00000000000000000000000000001000 V"
b00000000000000000000000000001001 W"
b10001110 X"
b1000111001000111101000111101000111101000011101000011101000011101 Y"
b11111111111111111111111111111101 ["
b00000000000000000000000000000001 \"
b00000000 ]"
b00000000 ^"
b00000000 _"
b00000000 `"
b00000000 a"
b00000000 b"
b00000000 c"
b00000000 d"
b00000000 e"
b00000000 f"
b00000000 g"
b00000000 h"
b00000000 i"
b00000000 j"
b00000000 k"
b00000000 l"
b00000000 m"
b00000000 n"
b00000000 o"
b00000000 p"
b00000000 q"
b00000000 r"
b00000000 s"
b00000000 t"
b00000000000000000000000000000010 u"
b00000000000000000000000010001110 v"
b000000000000000000000000 w"
b11111111111111111111111111111110 x"
0y"
0z"
0{"
0|"
0}"
0~"
b00000000000000000000000000000000 !#
#10
b001 -
b00000000000000000000000000001001 /
b01000111 0
b01000111 1
b01000111 2
b01000111 3
b01000111 4
b01000111 5
b01000111 6
b01000111 7
b01000111 8
b01000111 9
b01000111 :
b01000111 ;
b01000111 <
b01000111 =
b01000111 A
b01000111 B
b01000111 C
b01000111 D
b01000111 E
b01000111 F
b01000111 G
b01000111 H
b01000111 I
b01000111 M
b01000111 N
b01000111 O
b01000111 P
b01000111 Q
b01000111 R
b01000111 S
b01000111 T
b01000111 U
b01000111 Y
b01000111 Z
b01000111 [
b01000111 \
b01000111 ]
b01000111 ^
b01000111 _
b01000111 `
b01000111 a
b01000111 e
b01000111 f
b01000111 g
b01000111 h
b01000111 i
b01000111 j
b01000111 k
b01000111 l
b01000111 m
b01000111 q
b01000111 r
b01000111 s
b01000111 t
b01000111 u
b01000111 v
b01000111 w
b01000111 x
b01000111 y
b01000111 }
b01000111 ~
b01000111 !!
b01000111 "!
b01000111 #!
b01000111 $!
b01000111 %!
b01000111 &!
b01000111 '!
b01000111 +!
b01000111 ,!
b01000111 -!
b01000111 .!
b01000111 /!
b01000111 0!
b01000111 1!
b01000111 2!
b01000111 3!
b01000111 7!
b01000111 8!
b01000111 9!
b01000111 :!
b01000111 ;!
b01000111 <!
b01000111 =!
b01000111 >!
b01000111 ?!
b01000111 C!
b01000111 D!
b01000111 E!
b01000111 F!
b01000111 G!
b01000111 H!
b01000111 I!
b01000111 J!
b01000111 K!
b01000111 O!
b01000111 P!
b01000111 Q!
b01000111 R!
b01000111 S!
b01000111 T!
b01000111 U!
b01000111 V!
b01000111 W!
b01000111 X!
b00000000000000000000000001000111 Y!
b00000000000000000000000001000111 Z!
b00000000000000000000000001000111 [!
b01000111 \!
b10001110010001110100011101000111 ]!
b01000111 ^!
b10001110010001110100011101000111 _!
b01000111 `!
b10001110010001110100011101000111 a!
b01000111 b!
b10001110010001110100011101000111 c!
1d!
1h!
1l!
1p!
b00000000000000000000000000000001 t!
1v!
b01000111 w!
b01000111 x!
b01000111 y!
b01000111 z!
b01000111 {!
b01000111 |!
b01000111 }!
b01000111 ~!
b01000111 !"
b01000111 ""
b01000111 #"
b01000111 $"
b01000111 ("
b01000111 )"
b01000111 *"
b01000111 +"
b01000111 ,"
b01000111 -"
b01000111 ."
b01000111 /"
b01000111 3"
b01000111 4"
b01000111 5"
b01000111 6"
b01000111 7"
b01000111 8"
b01000111 9"
b01000111 :"
b01000111 ;"
0<"
1="
0@"
1C"
1D"
0G"
1J"
0K"
1L"
0O"
1R"
0S"
1T"
#15
0T"
#20
b010 -
b10100011 0
b10001110 1
b10100011 2
b10100011 3
b10100011 4
b10100011 5
b10001110 6
b10100011 7
b10100011 8
b10100011 9
b10100011 :
b10100011 ;
b10100011 <
b10100011 =
b10001110 A
b10001110 B
b10001110 C
b10100011 D
b10100011 E
b10100011 F
b10100011 G
b10100011 H
b10100011 I
b10001110 M
b10001110 N
b10001110 O
b10100011 P
b10100011 Q
b10100011 R
b10100011 S
b10100011 T
b10100011 U
b10001110 Y
b10001110 Z
b10001110 [
b10100011 \
b10100011 ]
b10100011 ^
b10100011 _
b10100011 `
b10100011 a
b10001110 e
b10001110 f
b10001110 g
b10100011 h
b10100011 i
b10100011 j
b10100011 k
b10100011 l
b10100011 m
b10001110 q
b10001110 r
b10001110 s
b10100011 t
b10100011 u
b10100011 v
b10100011 w
b10100011 x
b10100011 y
b10001110 }
b10001110 ~
b10001110 !!
b10100011 "!
b10100011 #!
b10100011 $!
b10100011 %!
b10100011 &!
b10100011 '!
b10001110 +!
b10001110 ,!
b10001110 -!
b10100011 .!
b10100011 /!
b10100011 0!
b10100011 1!
b10100011 2!
b10100011 3!
b10001110 7!
b10001110 8!
b10001110 9!
b10100011 :!
b10100011 ;!
b10100011 <!
b10100011 =!
b10100011 >!
b10100011 ?!
b10001110 C!
b10001110 D!
b10001110 E!
b10100011 F!
b10100011 G!
b10100011 H!
b10100011 I!
b10100011 J!
b10100011 K!
b10001110 O!
b10001110 P!
b10001110 Q!
b10100011 R!
b10100011 S!
b10100011 T!
b10100011 U!
b10100011 V!
b10100011 W!
b10100011 X!
b00000000000000000000000010001110 Y!
b00000000000000000000000010100011 Z!
b00000000000000000000000010100011 [!
b10100011 \!
b10001110100011101010001110100011 ]!
b10100011 ^!
b10001110100011101010001110100011 _!
b10100011 `!
b10001110100011101010001110100011 a!
b10100011 b!
b10001110100011101010001110100011 c!
1e!
1i!
1m!
1q!
b00000000000000000000000000000010 t!
b10100011 w!
b10100011 x!
b10100011 y!
b10100011 z!
b10100011 {!
b10100011 |!
b10100011 }!
b10100011 ~!
b10100011 !"
b10100011 ""
b10100011 #"
b10100011 $"
b10001110 ("
b10001110 )"
b10001110 *"
b10100011 +"
b10100011 ,"
b10100011 -"
b10100011 ."
b10100011 /"
b10001110 3"
b10001110 4"
b10001110 5"
b10100011 6"
b10100011 7"
b10100011 8"
b10100011 9"
b10100011 :"
b10100011 ;"
1<"
0="
1>"
0A"
0F"
1I"
0J"
1K"
0N"
1Q"
0R"
1S"
1T"
#25
0T"
#30
b011 -
b11010001 0
b01000111 1
b11010001 2
b11010001 3
b11010001 4
b11010001 5
b01000111 6
b11010001 7
b11010001 8
b11010001 9
b11010001 :
b11010001 ;
b11010001 <
b11010001 =
b01000111 A
b01000111 B
b01000111 C
b11010001 D
b11010001 E
b11010001 F
b11010001 G
b11010001 H
b11010001 I
b01000111 M
b01000111 N
b01000111 O
b11010001 P
b11010001 Q
b11010001 R
b11010001 S
b11010001 T
b11010001 U
b01000111 Y
b01000111 Z
b01000111 [
b11010001 \
b11010001 ]
b11010001 ^
b11010001 _
b11010001 `
b11010001 a
b01000111 e
b01000111 f
b01000111 g
b11010001 h
b11010001 i
b11010001 j
b11010001 k
b11010001 l
b11010001 m
b01000111 q
b01000111 r
b01000111 s
b11010001 t
b11010001 u
b11010001 v
b11010001 w
b11010001 x
b11010001 y
b01000111 }
b01000111 ~
b01000111 !!
b11010001 "!
b11010001 #!
b11010001 $!
b11010001 %!
b11010001 &!
b11010001 '!
b01000111 +!
b01000111 ,!
b01000111 -!
b11010001 .!
b11010001 /!
b11010001 0!
b11010001 1!
b11010001 2!
b11010001 3!
b01000111 7!
b01000111 8!
b01000111 9!
b11010001 :!
b11010001 ;!
b11010001 <!
b11010001 =!
b11010001 >!
b11010001 ?!
b01000111 C!
b01000111 D!
b01000111 E!
b11010001 F!
b11010001 G!
b11010001 H!
b11010001 I!
b11010001 J!
b11010001 K!
b01000111 O!
b01000111 P!
b01000111 Q!
b11010001 R!
b11010001 S!
b11010001 T!
b11010001 U!
b11010001 V!
b11010001 W!
b11010001 X!
b00000000000000000000000001000111 Y!
b00000000000000000000000011010001 Z!
b00000000000000000000000011010001 [!
b11010001 \!
b10001110010001111101000111010001 ]!
b11010001 ^!
b10001110010001111101000111010001 _!
b11010001 `!
b10001110010001111101000111010001 a!
b11010001 b!
b10001110010001111101000111010001 c!
1f!
1j!
1n!
1r!
b00000000000000000000000000000011 t!
b11010001 w!
b11010001 x!
b11010001 y!
b11010001 z!
b11010001 {!
b11010001 |!
b11010001 }!
b11010001 ~!
b11010001 !"
b11010001 ""
b11010001 #"
b11010001 $"
b01000111 ("
b01000111 )"
b01000111 *"
b11010001 +"
b11010001 ,"
b11010001 -"
b11010001 ."
b11010001 /"
b01000111 3"
b01000111 4"
b01000111 5"
b11010001 6"
b11010001 7"
b11010001 8"
b11010001 9"
b11010001 :"
b11010001 ;"
1="
0>"
1?"
0B"
0E"
1H"
0I"
1J"
0M"
1P"
0Q"
1R"
1T"
#35
0T"
#40
b100 -
b11101000 0
b10001110 1
b10001110 2
b11101000 3
b10001110 4
b11101000 5
b10001110 6
b10001110 7
b10001110 8
b10001110 9
b10001110 :
b11101000 ;
b11101000 <
b11101000 =
b10001110 A
b10001110 B
b10001110 C
b10001110 D
b10001110 E
b10001110 F
b11101000 G
b11101000 H
b11101000 I
b10001110 M
b10001110 N
b10001110 O
b10001110 P
b10001110 Q
b10001110 R
b11101000 S
b11101000 T
b11101000 U
b10001110 Y
b10001110 Z
b10001110 [
b10001110 \
b10001110 ]
b10001110 ^
b11101000 _
b11101000 `
b11101000 a
b10001110 e
b10001110 f
b10001110 g
b10001110 h
b10001110 i
b10001110 j
b11101000 k
b11101000 l
b11101000 m
b10001110 q
b10001110 r
b10001110 s
b10001110 t
b10001110 u
b10001110 v
b11101000 w
b11101000 x
b11101000 y
b10001110 }
b10001110 ~
b10001110 !!
b10001110 "!
b10001110 #!
b10001110 $!
b11101000 %!
b11101000 &!
b11101000 '!
b10001110 +!
b10001110 ,!
b10001110 -!
b10001110 .!
b10001110 /!
b10001110 0!
b11101000 1!
b11101000 2!
b11101000 3!
b10001110 7!
b10001110 8!
b10001110 9!
b10001110 :!
b10001110 ;!
b10001110 <!
b11101000 =!
b11101000 >!
b11101000 ?!
b10001110 C!
b10001110 D!
b10001110 E!
b10001110 F!
b10001110 G!
b10001110 H!
b11101000 I!
b11101000 J!
b11101000 K!
b10001110 O!
b10001110 P!
b10001110 Q!
b10001110 R!
b10001110 S!
b10001110 T!
b11101000 U!
b11101000 V!
b11101000 W!
b11101000 X!
b00000000000000000000000010001110 Y!
b00000000000000000000000010001110 Z!
b00000000000000000000000011101000 [!
b11101000 \!
b10001110100011101000111011101000 ]!
b11101000 ^!
b10001110100011101000111011101000 _!
b11101000 `!
b10001110100011101000111011101000 a!
b11101000 b!
b10001110100011101000111011101000 c!
1g!
1k!
1o!
1s!
b00000000000000000000000000000100 t!
b11101000 w!
b11101000 x!
b11101000 y!
b11101000 z!
b11101000 {!
b11101000 |!
b11101000 }!
b11101000 ~!
b11101000 !"
b11101000 ""
b11101000 #"
b11101000 $"
b10001110 ("
b10001110 )"
b10001110 *"
b10001110 +"
b10001110 ,"
b10001110 -"
b11101000 ."
b11101000 /"
b10001110 3"
b10001110 4"
b10001110 5"
b10001110 6"
b10001110 7"
b10001110 8"
b11101000 9"
b11101000 :"
b11101000 ;"
1>"
0?"
1@"
0C"
0D"
1G"
0H"
1I"
0L"
1O"
0P"
1Q"
1T"
#45
0T"
#50
b101 -
b01110100 0
b01000111 1
b01000111 2
b01110100 3
b01000111 4
b01110100 5
b01000111 6
b01000111 7
b01000111 8
b01000111 9
b01000111 :
b01110100 ;
b01110100 <
b01110100 =
b01000111 A
b01000111 B
b01000111 C
b01000111 D
b01000111 E
b01000111 F
b01110100 G
b01110100 H
b01110100 I
b01000111 M
b01000111 N
b01000111 O
b01000111 P
b01000111 Q
b01000111 R
b01110100 S
b01110100 T
b01110100 U
b01000111 Y
b01000111 Z
b01000111 [
b01000111 \
b01000111 ]
b01000111 ^
b01110100 _
b01110100 `
b01110100 a
b01000111 e
b01000111 f
b01000111 g
b01000111 h
b01000111 i
b01000111 j
b01110100 k
b01110100 l
b01110100 m
b01000111 q
b01000111 r
b01000111 s
b01000111 t
b01000111 u
b01000111 v
b01110100 w
b01110100 x
b01110100 y
b01000111 }
b01000111 ~
b01000111 !!
b01000111 "!
b01000111 #!
b01000111 $!
b01110100 %!
b01110100 &!
b01110100 '!
b01000111 +!
b01000111 ,!
b01000111 -!
b01000111 .!
b01000111 /!
b01000111 0!
b01110100 1!
b01110100 2!
b01110100 3!
b01000111 7!
b01000111 8!
b01000111 9!
b01000111 :!
b01000111 ;!
b01000111 <!
b01110100 =!
b01110100 >!
b01110100 ?!
b01000111 C!
b01000111 D!
b01000111 E!
b01000111 F!
b01000111 G!
b01000111 H!
b01110100 I!
b01110100 J!
b01110100 K!
b01000111 O!
b01000111 P!
b01000111 Q!
b01000111 R!
b01000111 S!
b01000111 T!
b01110100 U!
b01110100 V!
b01110100 W!
b01110100 X!
b00000000000000000000000001000111 Y!
b00000000000000000000000001000111 Z!
b00000000000000000000000001110100 [!
b01110100 \!
b10001110010001110100011101110100 ]!
b01110100 ^!
b10001110010001110100011101110100 _!
b01110100 `!
b10001110010001110100011101110100 a!
b01110100 b!
b10001110010001110100011101110100 c!
b00000000000000000000000000000101 t!
b01110100 w!
b01110100 x!
b01110100 y!
b01110100 z!
b01110100 {!
b01110100 |!
b01110100 }!
b01110100 ~!
b01110100 !"
b01110100 ""
b01110100 #"
b01110100 $"
b01000111 ("
b01000111 )"
b01000111 *"
b01000111 +"
b01000111 ,"
b01000111 -"
b01110100 ."
b01110100 /"
b01000111 3"
b01000111 4"
b01000111 5"
b01000111 6"
b01000111 7"
b01000111 8"
b01110100 9"
b01110100 :"
b01110100 ;"
0<"
1?"
0@"
1A"
1F"
0G"
1H"
0K"
1N"
0O"
1P"
0S"
1T"
#55
0T"
#60
b110 -
b00111010 0
b10001110 1
b10100011 2
b00111010 3
b10100011 4
b00111010 5
b10001110 6
b10100011 7
b10100011 8
b10100011 9
b10100011 :
b00111010 ;
b00111010 <
b00111010 =
b10001110 A
b10001110 B
b10001110 C
b10100011 D
b10100011 E
b10100011 F
b00111010 G
b00111010 H
b00111010 I
b10001110 M
b10001110 N
b10001110 O
b10100011 P
b10100011 Q
b10100011 R
b00111010 S
b00111010 T
b00111010 U
b10001110 Y
b10001110 Z
b10001110 [
b10100011 \
b10100011 ]
b10100011 ^
b00111010 _
b00111010 `
b00111010 a
b10001110 e
b10001110 f
b10001110 g
b10100011 h
b10100011 i
b10100011 j
b00111010 k
b00111010 l
b00111010 m
b10001110 q
b10001110 r
b10001110 s
b10100011 t
b10100011 u
b10100011 v
b00111010 w
b00111010 x
b00111010 y
b10001110 }
b10001110 ~
b10001110 !!
b10100011 "!
b10100011 #!
b10100011 $!
b00111010 %!
b00111010 &!
b00111010 '!
b10001110 +!
b10001110 ,!
b10001110 -!
b10100011 .!
b10100011 /!
b10100011 0!
b00111010 1!
b00111010 2!
b00111010 3!
b10001110 7!
b10001110 8!
b10001110 9!
b10100011 :!
b10100011 ;!
b10100011 <!
b00111010 =!
b00111010 >!
b00111010 ?!
b10001110 C!
b10001110 D!
b10001110 E!
b10100011 F!
b10100011 G!
b10100011 H!
b00111010 I!
b00111010 J!
b00111010 K!
b10001110 O!
b10001110 P!
b10001110 Q!
b10100011 R!
b10100011 S!
b10100011 T!
b00111010 U!
b00111010 V!
b00111010 W!
b00111010 X!
b00000000000000000000000010001110 Y!
b00000000000000000000000010100011 Z!
b00000000000000000000000000111010 [!
b00111010 \!
b10001110100011101010001100111010 ]!
b00111010 ^!
b10001110100011101010001100111010 _!
b00111010 `!
b10001110100011101010001100111010 a!
b00111010 b!
b10001110100011101010001100111010 c!
b00000000000000000000000000000110 t!
b00111010 w!
b00111010 x!
b00111010 y!
b00111010 z!
b00111010 {!
b00111010 |!
b00111010 }!
b00111010 ~!
b00111010 !"
b00111010 ""
b00111010 #"
b00111010 $"
b10001110 ("
b10001110 )"
b10001110 *"
b10100011 +"
b10100011 ,"
b10100011 -"
b00111010 ."
b00111010 /"
b10001110 3"
b10001110 4"
b10001110 5"
b10100011 6"
b10100011 7"
b10100011 8"
b00111010 9"
b00111010 :"
b00111010 ;"
0="
1@"
0A"
1B"
1E"
0F"
1G"
0J"
1M"
0N"
1O"
0R"
1T"
#65
0T"
#70
b111 -
b00011101 0
b01000111 1
b11010001 2
b00011101 3
b11010001 4
b00011101 5
b01000111 6
b11010001 7
b11010001 8
b11010001 9
b11010001 :
b00011101 ;
b00011101 <
b00011101 =
b01000111 A
b01000111 B
b01000111 C
b11010001 D
b11010001 E
b11010001 F
b00011101 G
b00011101 H
b00011101 I
b01000111 M
b01000111 N
b01000111 O
b11010001 P
b11010001 Q
b11010001 R
b00011101 S
b00011101 T
b00011101 U
b01000111 Y
b01000111 Z
b01000111 [
b11010001 \
b11010001 ]
b11010001 ^
b00011101 _
b00011101 `
b00011101 a
b01000111 e
b01000111 f
b01000111 g
b11010001 h
b11010001 i
b11010001 j
b00011101 k
b00011101 l
b00011101 m
b01000111 q
b01000111 r
b01000111 s
b11010001 t
b11010001 u
b11010001 v
b00011101 w
b00011101 x
b00011101 y
b01000111 }
b01000111 ~
b01000111 !!
b11010001 "!
b11010001 #!
b11010001 $!
b00011101 %!
b00011101 &!
b00011101 '!
b01000111 +!
b01000111 ,!
b01000111 -!
b11010001 .!
b11010001 /!
b11010001 0!
b00011101 1!
b00011101 2!
b00011101 3!
b01000111 7!
b01000111 8!
b01000111 9!
b11010001 :!
b11010001 ;!
b11010001 <!
b00011101 =!
b00011101 >!
b00011101 ?!
b01000111 C!
b01000111 D!
b01000111 E!
b11010001 F!
b11010001 G!
b11010001 H!
b00011101 I!
b00011101 J!
b00011101 K!
b01000111 O!
b01000111 P!
b01000111 Q!
b11010001 R!
b11010001 S!
b11010001 T!
b00011101 U!
b00011101 V!
b00011101 W!
b00011101 X!
b00000000000000000000000001000111 Y!
b00000000000000000000000011010001 Z!
b00000000000000000000000000011101 [!
b00011101 \!
b10001110010001111101000100011101 ]!
b00011101 ^!
b10001110010001111101000100011101 _!
b00011101 `!
b10001110010001111101000100011101 a!
b00011101 b!
b10001110010001111101000100011101 c!
b00000000000000000000000000000111 t!
b00011101 w!
b00011101 x!
b00011101 y!
b00011101 z!
b00011101 {!
b00011101 |!
b00011101 }!
b00011101 ~!
b00011101 !"
b00011101 ""
b00011101 #"
b00011101 $"
b01000111 ("
b01000111 )"
b01000111 *"
b11010001 +"
b11010001 ,"
b11010001 -"
b00011101 ."
b00011101 /"
b01000111 3"
b01000111 4"
b01000111 5"
b11010001 6"
b11010001 7"
b11010001 8"
b00011101 9"
b00011101 :"
b00011101 ;"
0>"
1A"
0B"
1C"
1D"
0E"
1F"
0I"
1L"
0M"
1N"
0Q"
1T"
#75
0T"
#80
b000 -
b10001110 0
b10001110 1
b10001110 2
b10001110 3
b10001110 4
b10001110 5
b10001110 6
b10001110 7
b10001110 8
b10001110 9
b10001110 :
b10001110 ;
b10001110 <
b10001110 =
b10001110 A
b10001110 B
b10001110 C
b10001110 D
b10001110 E
b10001110 F
b10001110 G
b10001110 H
b10001110 I
b10001110 M
b10001110 N
b10001110 O
b10001110 P
b10001110 Q
b10001110 R
b10001110 S
b10001110 T
b10001110 U
b10001110 Y
b10001110 Z
b10001110 [
b10001110 \
b10001110 ]
b10001110 ^
b10001110 _
b10001110 `
b10001110 a
b10001110 e
b10001110 f
b10001110 g
b10001110 h
b10001110 i
b10001110 j
b10001110 k
b10001110 l
b10001110 m
b10001110 q
b10001110 r
b10001110 s
b10001110 t
b10001110 u
b10001110 v
b10001110 w
b10001110 x
b10001110 y
b10001110 }
b10001110 ~
b10001110 !!
b10001110 "!
b10001110 #!
b10001110 $!
b10001110 %!
b10001110 &!
b10001110 '!
b10001110 +!
b10001110 ,!
b10001110 -!
b10001110 .!
b10001110 /!
b10001110 0!
b10001110 1!
b10001110 2!
b10001110 3!
b10001110 7!
b10001110 8!
b10001110 9!
b10001110 :!
b10001110 ;!
b10001110 <!
b10001110 =!
b10001110 >!
b10001110 ?!
b10001110 C!
b10001110 D!
b10001110 E!
b10001110 F!
b10001110 G!
b10001110 H!
b10001110 I!
b10001110 J!
b10001110 K!
b10001110 O!
b10001110 P!
b10001110 Q!
b10001110 R!
b10001110 S!
b10001110 T!
b10001110 U!
b10001110 V!
b10001110 W!
b10001110 X!
b00000000000000000000000010001110 Y!
b00000000000000000000000010001110 Z!
b00000000000000000000000010001110 [!
b10001110 \!
b10001110100011101000111010001110 ]!
b10001110 ^!
b10001110100011101000111010001110 _!
b10001110 `!
b10001110100011101000111010001110 a!
b10001110 b!
b10001110100011101000111010001110 c!
b00000000000000000000000000001000 t!
b10001110 w!
b10001110 x!
b10001110 y!
b10001110 z!
b10001110 {!
b10001110 |!
b10001110 }!
b10001110 ~!
b10001110 !"
b10001110 ""
b10001110 #"
b10001110 $"
b10001110 ("
b10001110 )"
b10001110 *"
b10001110 +"
b10001110 ,"
b10001110 -"
b10001110 ."
b10001110 /"
b10001110 3"
b10001110 4"
b10001110 5"
b10001110 6"
b10001110 7"
b10001110 8"
b10001110 9"
b10001110 :"
b10001110 ;"
1<"
0?"
1B"
0C"
0D"
1E"
0H"
1K"
0L"
1M"
0P"
1S"
1T"
