$version Generated by VerilatedVcd $end
$date Sat Jan 27 15:03:24 2018
 $end
$timescale   1ps $end

 $scope module top $end
  $var wire  1 ' clk $end
  $scope module t $end
   $var wire 32 $ c_trace_on [31:0] $end
   $var wire  1 ' clk $end
   $var wire 32 # cyc [31:0] $end
   $var real 64 % r $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000001 #
b00000000000000000000000000000000 $
r0 %
0'
#10
b00000000000000000000000000000010 #
r0.1 %
1'
#15
0'
#20
b00000000000000000000000000000011 #
b00000000000000000000000000000001 $
r0.2 %
1'
#25
0'
#30
b00000000000000000000000000000100 #
b00000000000000000000000000000010 $
r0.3 %
1'
#35
0'
#40
b00000000000000000000000000000101 #
b00000000000000000000000000000011 $
r0.4 %
1'
#45
0'
#50
b00000000000000000000000000000110 #
b00000000000000000000000000000100 $
r0.5 %
1'
#55
0'
#60
b00000000000000000000000000000111 #
b00000000000000000000000000000101 $
r0.6 %
1'
#65
0'
#70
b00000000000000000000000000001000 #
b00000000000000000000000000000110 $
r0.7 %
1'
#75
0'
#80
b00000000000000000000000000001001 #
b00000000000000000000000000000111 $
r0.7999999999999999 %
1'
#85
0'
#90
b00000000000000000000000000001010 #
b00000000000000000000000000001000 $
r0.8999999999999999 %
1'
#95
0'
#100
1'
b00000000000000000000000000001011 #
b00000000000000000000000000001001 $
r0.9999999999999999 %
