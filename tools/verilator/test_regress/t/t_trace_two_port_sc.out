$version Generated by VerilatedVcd $end
$date Sun Mar  1 21:32:22 2020
 $end
$timescale   1ps $end

 $scope module topa $end
  $scope module t $end
   $var wire 32 3 c_trace_on [31:0] $end
   $var wire  1 # clk $end
   $var wire 32 + cyc [31:0] $end
   $scope module sub $end
    $var wire 32 ; inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
b00000000000000000000000000000001 +
b00000000000000000000000000000000 3
b00000000000000000000000000000001 ;
#10000
1#
b00000000000000000000000000000010 +
b00000000000000000000000000000011 3
#15000
0#
#20000
1#
b00000000000000000000000000000011 +
b00000000000000000000000000000100 3
#25000
0#
#30000
1#
b00000000000000000000000000000100 +
b00000000000000000000000000000101 3
#35000
0#
#40000
1#
b00000000000000000000000000000101 +
b00000000000000000000000000000110 3
#45000
0#
#50000
1#
b00000000000000000000000000000110 +
b00000000000000000000000000000111 3
#55000
0#
#60000
1#
b00000000000000000000000000000111 +
b00000000000000000000000000001000 3
#65000
0#
#70000
1#
b00000000000000000000000000001000 +
b00000000000000000000000000001001 3
#75000
0#
#80000
1#
b00000000000000000000000000001001 +
b00000000000000000000000000001010 3
#85000
0#
#90000
1#
b00000000000000000000000000001010 +
b00000000000000000000000000001011 3
#95000
0#
#100000
1#
b00000000000000000000000000001011 +
b00000000000000000000000000001100 3
