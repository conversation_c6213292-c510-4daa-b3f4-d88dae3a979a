$version Generated by VerilatedVcd $end
$date Sat Mar  7 18:38:11 2020
 $end
$timescale   1ps $end

 $scope module topa $end
  $var wire  1 3 clk $end
  $scope module t $end
   $var wire 32 + c_trace_on [31:0] $end
   $var wire  1 3 clk $end
   $var wire 32 # cyc [31:0] $end
   $scope module sub $end
    $var wire 32 ; inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#10
b00000000000000000000000000000001 #
b00000000000000000000000000000000 +
13
b00000000000000000000000000000001 ;
#10
#15
#15
03
#20
#20
b00000000000000000000000000000010 #
b00000000000000000000000000000011 +
13
#25
#25
03
#30
#30
b00000000000000000000000000000011 #
b00000000000000000000000000000100 +
13
#35
#35
03
#40
#40
b00000000000000000000000000000100 #
b00000000000000000000000000000101 +
13
#45
#45
03
#50
#50
b00000000000000000000000000000101 #
b00000000000000000000000000000110 +
13
#55
#55
03
#60
#60
b00000000000000000000000000000110 #
b00000000000000000000000000000111 +
13
#65
#65
03
#70
#70
b00000000000000000000000000000111 #
b00000000000000000000000000001000 +
13
#75
#75
03
#80
#80
b00000000000000000000000000001000 #
b00000000000000000000000000001001 +
13
#85
#85
03
#90
#90
b00000000000000000000000000001001 #
b00000000000000000000000000001010 +
13
#95
#95
03
#100
#100
b00000000000000000000000000001010 #
b00000000000000000000000000001011 +
13
#105
#105
03
#110
#110
13
b00000000000000000000000000001011 #
b00000000000000000000000000001100 +
