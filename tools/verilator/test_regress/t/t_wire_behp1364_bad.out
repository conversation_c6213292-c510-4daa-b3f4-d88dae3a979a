%Error-PROCASSWIRE: t/t_wire_behp1364_bad.v:23:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'w'
                                                : ... In instance t
   23 |       w = '0;   
      |       ^
                    ... For error description see https://verilator.org/warn/PROCASSWIRE?v=latest
%Error-PROCASSWIRE: t/t_wire_behp1364_bad.v:24:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'o'
                                                : ... In instance t
   24 |       o = '0;   
      |       ^
%Error-PROCASSWIRE: t/t_wire_behp1364_bad.v:25:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'oa'
                                                : ... In instance t
   25 |       oa = '0;   
      |       ^~
%Error: Exiting due to
