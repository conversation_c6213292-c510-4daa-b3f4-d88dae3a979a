$version Generated by VerilatedVcd $end
$date Wed Aug 11 12:41:11 2021 $end
$timescale 1ps $end

 $scope module top $end
  $var wire  1 = clk $end
  $scope module $unit $end
   $var wire  1 # global_bit $end
  $upscope $end
  $scope module t $end
   $var wire  1 G LONGSTART_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_LONGEND $end
   $var wire  1 = clk $end
   $var wire 32 $ cyc [31:0] $end
   $var wire  8 E unpacked_array[-1] [7:0] $end
   $var wire  8 D unpacked_array[-2] [7:0] $end
   $var wire  8 F unpacked_array[0] [7:0] $end
   $var real 64 1 v_arr_real[0] $end
   $var real 64 3 v_arr_real[1] $end
   $var wire  2 ( v_arrp [2:1] $end
   $var wire  4 ) v_arrp_arrp [3:0] $end
   $var wire  4 * v_arrp_strp [3:0] $end
   $var wire  1 > v_arru[1] $end
   $var wire  1 ? v_arru[2] $end
   $var wire  2 + v_arru_arrp[3] [2:1] $end
   $var wire  2 , v_arru_arrp[4] [2:1] $end
   $var wire  1 @ v_arru_arru[3][1] $end
   $var wire  1 A v_arru_arru[3][2] $end
   $var wire  1 B v_arru_arru[4][1] $end
   $var wire  1 C v_arru_arru[4][2] $end
   $var wire  2 - v_arru_strp[3] [1:0] $end
   $var wire  2 . v_arru_strp[4] [1:0] $end
   $var wire  3 9 v_enumb [2:0] $end
   $var wire  6 : v_enumb2_str [5:0] $end
   $var wire 32 7 v_enumed [31:0] $end
   $var wire 32 8 v_enumed2 [31:0] $end
   $var real 64 / v_real $end
   $var wire 64 5 v_str32x2 [63:0] $end
   $var wire  2 % v_strp [1:0] $end
   $var wire  4 & v_strp_strp [3:0] $end
   $var wire  2 ' v_unip_strp [1:0] $end
   $scope module a_module_instantiation_with_a_very_long_name_that_once_its_signals_get_concatenated_and_inlined_will_almost_certainly_result_in_them_getting_hashed $end
    $var wire 32 J PARAM [31:0] $end
   $upscope $end
   $scope module p2 $end
    $var wire 32 H PARAM [31:0] $end
   $upscope $end
   $scope module p3 $end
    $var wire 32 I PARAM [31:0] $end
   $upscope $end
   $scope module unnamedblk1 $end
    $var wire 32 ; b [31:0] $end
    $scope module unnamedblk2 $end
     $var wire 32 < a [31:0] $end
    $upscope $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1#
b00000000000000000000000000000000 $
b00 %
b0000 &
b00 '
b00 (
b0000 )
b0000 *
b00 +
b00 ,
b00 -
b00 .
r0 /
r0 1
r0 3
b0000000000000000000000000000000000000000000000000000000011111111 5
b00000000000000000000000000000000 7
b00000000000000000000000000000000 8
b000 9
b000000 :
b00000000000000000000000000000000 ;
b00000000000000000000000000000000 <
0=
0>
0?
0@
0A
0B
0C
b00000000 D
b00000000 E
b00000000 F
0G
b00000000000000000000000000000010 H
b00000000000000000000000000000011 I
b00000000000000000000000000000100 J
#10
b00000000000000000000000000000001 $
b11 %
b1111 &
b11 '
b11 (
b1111 )
b1111 *
b11 +
b11 ,
b11 -
b11 .
r0.1 /
r0.2 1
r0.3 3
b0000000000000000000000000000000100000000000000000000000011111110 5
b00000000000000000000000000000001 7
b00000000000000000000000000000010 8
b111 9
b00000000000000000000000000000101 ;
b00000000000000000000000000000101 <
1=
#15
0=
#20
b00000000000000000000000000000010 $
b00 %
b0000 &
b00 '
b00 (
b0000 )
b0000 *
b00 +
b00 ,
b00 -
b00 .
r0.2 /
r0.4 1
r0.6 3
b0000000000000000000000000000001000000000000000000000000011111101 5
b00000000000000000000000000000010 7
b00000000000000000000000000000100 8
b110 9
b111111 :
1=
#25
0=
#30
b00000000000000000000000000000011 $
b11 %
b1111 &
b11 '
b11 (
b1111 )
b1111 *
b11 +
b11 ,
b11 -
b11 .
r0.3 /
r0.6000000000000001 1
r0.8999999999999999 3
b0000000000000000000000000000001100000000000000000000000011111100 5
b00000000000000000000000000000011 7
b00000000000000000000000000000110 8
b101 9
b110110 :
1=
#35
0=
#40
b00000000000000000000000000000100 $
b00 %
b0000 &
b00 '
b00 (
b0000 )
b0000 *
b00 +
b00 ,
b00 -
b00 .
r0.4 /
r0.8 1
r1.2 3
b0000000000000000000000000000010000000000000000000000000011111011 5
b00000000000000000000000000000100 7
b00000000000000000000000000001000 8
b100 9
b101101 :
1=
#45
0=
#50
b00000000000000000000000000000101 $
b11 %
b1111 &
b11 '
b11 (
b1111 )
b1111 *
b11 +
b11 ,
b11 -
b11 .
r0.5 /
r1 1
r1.5 3
b0000000000000000000000000000010100000000000000000000000011111010 5
b00000000000000000000000000000101 7
b00000000000000000000000000001010 8
b011 9
b100100 :
1=
#55
0=
#60
b00000000000000000000000000000110 $
b00 %
b0000 &
b00 '
b00 (
b0000 )
b0000 *
b00 +
b00 ,
b00 -
b00 .
r0.6 /
r1.2 1
r1.8 3
b0000000000000000000000000000011000000000000000000000000011111001 5
b00000000000000000000000000000110 7
b00000000000000000000000000001100 8
b010 9
b011011 :
1=
