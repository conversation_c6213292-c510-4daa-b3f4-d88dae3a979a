%Warning-UNOPTFLAT: t/t_unoptflat_simple_2.v:15:15: Signal unoptimizable: Feedback to clock or circular logic: 't.x'
   15 |    wire [2:0] x;
      |               ^
                    ... For warning description see https://verilator.org/warn/UNOPTFLAT?v=latest
                    ... Use "/* verilator lint_off UNOPTFLAT */" and lint_on around source to disable this message.
                    t/t_unoptflat_simple_2.v:15:15:      Example path: t.x
                    t/t_unoptflat_simple_2.v:17:18:      Example path: ASSIGNW
                    t/t_unoptflat_simple_2.v:15:15:      Example path: t.x
                    ... Widest candidate vars to split:
                        t/t_unoptflat_simple_2.v:15:15:  t.x, width 3, fanout 10, can split_var
                    ... Most fanned out candidate vars to split:
                        t/t_unoptflat_simple_2.v:15:15:  t.x, width 3, fanout 10, can split_var
                    ... Suggest add /*verilator split_var*/ to appropriate variables above.
%Error: Exiting due to
