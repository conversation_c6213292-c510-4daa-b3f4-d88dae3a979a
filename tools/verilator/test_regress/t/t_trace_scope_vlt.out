$version Generated by VerilatedVcd $end
$date Thu May 12 22:13:21 2022 $end
$timescale 1ps $end

 $scope module top $end
  $var wire  1 ) clk $end
  $scope module t $end
   $var wire  1 ) clk $end
   $scope module sub1a $end
    $var wire 32 * ADD [31:0] $end
    $var wire 32 # cyc [31:0] $end
    $var wire 32 $ value [31:0] $end
   $upscope $end
   $scope module sub1b $end
    $var wire 32 + ADD [31:0] $end
    $var wire 32 # cyc [31:0] $end
    $var wire 32 % value [31:0] $end
    $scope module sub2a $end
     $var wire 32 # cyc [31:0] $end
     $var wire 32 & value [31:0] $end
    $upscope $end
    $scope module sub2b $end
     $var wire 32 , ADD [31:0] $end
     $var wire 32 # cyc [31:0] $end
     $var wire 32 ' value [31:0] $end
    $upscope $end
    $scope module sub2c $end
     $var wire 32 - ADD [31:0] $end
     $var wire 32 # cyc [31:0] $end
     $var wire 32 ( value [31:0] $end
    $upscope $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000000 #
b00000000000000000000000000001010 $
b00000000000000000000000000010100 %
b00000000000000000000000000010101 &
b00000000000000000000000000010110 '
b00000000000000000000000000010111 (
0)
b00000000000000000000000000001010 *
b00000000000000000000000000010100 +
b00000000000000000000000000010110 ,
b00000000000000000000000000010111 -
#10
b00000000000000000000000000000001 #
b00000000000000000000000000001011 $
b00000000000000000000000000010101 %
b00000000000000000000000000010110 &
b00000000000000000000000000010111 '
b00000000000000000000000000011000 (
1)
#15
0)
#20
b00000000000000000000000000000010 #
b00000000000000000000000000001100 $
b00000000000000000000000000010110 %
b00000000000000000000000000010111 &
b00000000000000000000000000011000 '
b00000000000000000000000000011001 (
1)
#25
0)
#30
b00000000000000000000000000000011 #
b00000000000000000000000000001101 $
b00000000000000000000000000010111 %
b00000000000000000000000000011000 &
b00000000000000000000000000011001 '
b00000000000000000000000000011010 (
1)
#35
0)
#40
b00000000000000000000000000000100 #
b00000000000000000000000000001110 $
b00000000000000000000000000011000 %
b00000000000000000000000000011001 &
b00000000000000000000000000011010 '
b00000000000000000000000000011011 (
1)
#45
0)
#50
b00000000000000000000000000000101 #
b00000000000000000000000000001111 $
b00000000000000000000000000011001 %
b00000000000000000000000000011010 &
b00000000000000000000000000011011 '
b00000000000000000000000000011100 (
1)
#55
0)
#60
b00000000000000000000000000000110 #
b00000000000000000000000000010000 $
b00000000000000000000000000011010 %
b00000000000000000000000000011011 &
b00000000000000000000000000011100 '
b00000000000000000000000000011101 (
1)
#65
0)
#70
b00000000000000000000000000000111 #
b00000000000000000000000000010001 $
b00000000000000000000000000011011 %
b00000000000000000000000000011100 &
b00000000000000000000000000011101 '
b00000000000000000000000000011110 (
1)
#75
0)
#80
b00000000000000000000000000001000 #
b00000000000000000000000000010010 $
b00000000000000000000000000011100 %
b00000000000000000000000000011101 &
b00000000000000000000000000011110 '
b00000000000000000000000000011111 (
1)
#85
0)
#90
b00000000000000000000000000001001 #
b00000000000000000000000000010011 $
b00000000000000000000000000011101 %
b00000000000000000000000000011110 &
b00000000000000000000000000011111 '
b00000000000000000000000000100000 (
1)
#95
0)
#100
b00000000000000000000000000001010 #
b00000000000000000000000000010100 $
b00000000000000000000000000011110 %
b00000000000000000000000000011111 &
b00000000000000000000000000100000 '
b00000000000000000000000000100001 (
1)
#105
0)
#110
b00000000000000000000000000001011 #
b00000000000000000000000000010101 $
b00000000000000000000000000011111 %
b00000000000000000000000000100000 &
b00000000000000000000000000100001 '
b00000000000000000000000000100010 (
1)
