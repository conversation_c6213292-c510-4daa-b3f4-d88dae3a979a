$date
	Wed Feb 23 00:03:49 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module topa $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var integer 32 " cyc [31:0] $end
$var integer 32 # c_trace_on [31:0] $end
$scope module sub $end
$var integer 32 $ inside_sub_a [31:0] $end
$upscope $end
$upscope $end
$upscope $end
$enddefinitions $end
#10
$dumpvars
b00000000000000000000000000000001 $
b00000000000000000000000000000000 #
b00000000000000000000000000000001 "
1!
$end
#15
0!
#20
1!
b00000000000000000000000000000010 "
b00000000000000000000000000000011 #
#25
0!
#30
1!
b00000000000000000000000000000100 #
b00000000000000000000000000000011 "
#35
0!
#40
1!
b00000000000000000000000000000100 "
b00000000000000000000000000000101 #
#45
0!
#50
1!
b00000000000000000000000000000110 #
b00000000000000000000000000000101 "
#55
0!
#60
1!
b00000000000000000000000000000110 "
b00000000000000000000000000000111 #
#65
0!
#70
1!
b00000000000000000000000000001000 #
b00000000000000000000000000000111 "
#75
0!
#80
1!
b00000000000000000000000000001000 "
b00000000000000000000000000001001 #
#85
0!
#90
1!
b00000000000000000000000000001010 #
b00000000000000000000000000001001 "
#95
0!
#100
1!
b00000000000000000000000000001010 "
b00000000000000000000000000001011 #
#105
0!
#110
1!
b00000000000000000000000000001100 #
b00000000000000000000000000001011 "
