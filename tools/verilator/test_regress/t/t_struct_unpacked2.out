%Warning-UNPACKED: t/t_struct_unpacked2.v:10:13: Unsupported: Unpacked array in packed struct/union (struct/union converted to unpacked)
   10 |       int b [2];
      |             ^
                   ... For warning description see https://verilator.org/warn/UNPACKED?v=latest
                   ... Use "/* verilator lint_off UNPACKED */" and lint_on around source to disable this message.
%Warning-UNPACKED: t/t_struct_unpacked2.v:9:12: Unsupported: Unpacked struct/union
                                              : ... In instance x
    9 |    typedef struct {
      |            ^~~~~~
%Error: Exiting due to
