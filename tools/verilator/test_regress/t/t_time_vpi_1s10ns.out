:: In top.t
Time scale of t is 1s / 10ns
[6000000000] time%0d=60  123%0t=12300000000
  dig%0t=500000000 dig%0d=5
  rdig%0t=543210988 rdig%0f=5.432110
  acc%0t=1234567890123456789000000000 acc%0d=12345678901234567890
[60000000000.000000ns] time%0d=60  123%0t=123000000000.000000ns
  dig%0t=5000000000.000000ns dig%0d=5
  rdig%0t=5432109876.543210ns rdig%0f=5.432110
  acc%0t=12345678901234567890000000000.000000ns acc%0d=12345678901234567890
[60000000000.000000ns] stime%0t=60000000000.000000ns  stime%0d=60  stime%0f=60.000000
[60000000000.000000ns] rtime%0t=60000000000.000000ns  rtime%0d=60  rtime%0f=60.000000
global vpiSimTime = 1,1705032704  vpiScaledRealTime = 6e+09
global vpiTimeUnit = 0  vpiTimePrecision = -8
top.t vpiSimTime = 1,1705032704  vpiScaledRealTime = 60
top.t vpiTimeUnit = 0  vpiTimePrecision = -8
*-* All Finished *-*
