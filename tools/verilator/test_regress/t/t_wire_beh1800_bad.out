%Error-PROCASSWIRE: t/t_wire_beh1800_bad.v:25:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'w'
                                               : ... In instance t
   25 |       w = '0;   
      |       ^
                    ... For error description see https://verilator.org/warn/PROCASSWIRE?v=latest
%Error-PROCASSWIRE: t/t_wire_beh1800_bad.v:26:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'o'
                                               : ... In instance t
   26 |       o = '0;   
      |       ^
%Error-PROCASSWIRE: t/t_wire_beh1800_bad.v:27:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'oa'
                                               : ... In instance t
   27 |       oa = '0;   
      |       ^~
%Error-PROCASSWIRE: t/t_wire_beh1800_bad.v:28:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'wo'
                                               : ... In instance t
   28 |       wo = '0;   
      |       ^~
%Error-PROCASSWIRE: t/t_wire_beh1800_bad.v:29:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2017 6.5): 'woa'
                                               : ... In instance t
   29 |       woa = '0;   
      |       ^~~
%Error: Exiting due to
