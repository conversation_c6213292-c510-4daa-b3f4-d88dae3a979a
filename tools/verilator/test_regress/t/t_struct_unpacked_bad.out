%Warning-UNPACKED: t/t_struct_unpacked_bad.v:9:12: Unsupported: Unpacked struct/union
                                                 : ... In instance x
    9 |    typedef struct {
      |            ^~~~~~
                   ... For warning description see https://verilator.org/warn/UNPACKED?v=latest
                   ... Use "/* verilator lint_off UNPACKED */" and lint_on around source to disable this message.
%Error: Exiting due to
