$version Generated by VerilatedVcd $end
$date Tue Apr  7 19:56:41 2020
 $end
$timescale   1ps $end

 $scope module top $end
  $scope module t $end
   $var wire 32 3 c_trace_on [31:0] $end
   $var wire  1 # clk $end
   $var wire 32 + cyc [31:0] $end
   $var real 64 ; r $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
b00000000000000000000000000000001 +
b00000000000000000000000000000000 3
r0 ;
#10
1#
b00000000000000000000000000000010 +
r0.1 ;
#11
#12
#13
#14
#15
0#
#16
#17
#18
#19
#20
1#
b00000000000000000000000000000011 +
b00000000000000000000000000000001 3
r0.2 ;
#21
#22
#23
#24
#25
0#
#26
#27
#28
#29
#30
1#
b00000000000000000000000000000100 +
b00000000000000000000000000000010 3
r0.3 ;
#31
#32
#33
#34
#35
0#
#36
#37
#38
#39
#40
1#
b00000000000000000000000000000101 +
b00000000000000000000000000000011 3
r0.4 ;
#41
#42
#43
#44
#45
0#
#46
#47
#48
#49
#50
1#
b00000000000000000000000000000110 +
b00000000000000000000000000000100 3
r0.5 ;
#51
#52
#53
#54
#55
0#
#56
#57
#58
#59
#60
1#
b00000000000000000000000000000111 +
b00000000000000000000000000000101 3
r0.6 ;
#61
#62
#63
#64
#65
0#
#66
#67
#68
#69
#70
1#
b00000000000000000000000000001000 +
b00000000000000000000000000000110 3
r0.7 ;
#71
#72
#73
#74
#75
0#
#76
#77
#78
#79
#80
1#
b00000000000000000000000000001001 +
b00000000000000000000000000000111 3
r0.7999999999999999 ;
#81
#82
#83
#84
#85
0#
#86
#87
#88
#89
#90
1#
b00000000000000000000000000001010 +
b00000000000000000000000000001000 3
r0.8999999999999999 ;
#91
#92
#93
#94
#95
0#
#96
#97
#98
#99
#100
1#
b00000000000000000000000000001011 +
b00000000000000000000000000001001 3
r0.9999999999999999 ;
#101
#102
#103
#104
