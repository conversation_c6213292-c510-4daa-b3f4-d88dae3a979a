%Warning-PINMISSING: t/t_udp_bad.v:10:10: Cell has missing pin: 'c_bad'
   10 |    udp_x x (a, b);
      |          ^
                     ... For warning description see https://verilator.org/warn/PINMISSING?v=latest
                     ... Use "/* verilator lint_off PINMISSING */" and lint_on around source to disable this message.
%Error: t/t_udp_bad.v:14:18: Pin is not an in/out/inout/interface: 'a_bad'
   14 | primitive udp_x (a_bad, b, c_bad);
      |                  ^~~~~
%Error-PINNOTFOUND: t/t_udp_bad.v:10:13: Pin not found: '__pinNumber1'
   10 |    udp_x x (a, b);
      |             ^
%Error: t/t_udp_bad.v:15:9: Only inputs and outputs are allowed in udp modules
   15 |    tri  a_bad;
      |         ^~~~~
%Error: t/t_udp_bad.v:17:11: Multiple outputs not allowed in udp modules
   17 |    output c_bad;
      |           ^~~~~
%Error: Exiting due to
