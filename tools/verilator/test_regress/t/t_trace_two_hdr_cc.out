$version Generated by VerilatedVcd $end
$date Sat Feb 29 09:09:40 2020
 $end
$timescale   1ps $end

 $scope module topa $end
  $var wire  1 3 clk $end
  $scope module t $end
   $var wire 32 + c_trace_on [31:0] $end
   $var wire  1 3 clk $end
   $var wire 32 # cyc [31:0] $end
   $scope module sub $end
    $var wire 32 ; inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
 $scope module topb $end
  $var wire  1 N clk $end
  $scope module t $end
   $var wire 32 ^ c_trace_on [31:0] $end
   $var wire  1 N clk $end
   $var wire 32 V cyc [31:0] $end
   $var real 64 > r $end
   $scope module sub $end
    $var wire 32 f inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000001 #
b00000000000000000000000000000000 +
03
b00000000000000000000000000000001 ;
r0 >
0N
b00000000000000000000000000000001 V
b00000000000000000000000000000000 ^
b00000000000000000000000000000010 f
#10
b00000000000000000000000000000010 #
b00000000000000000000000000000011 +
13
r0.1 >
1N
#15
03
0N
#20
b00000000000000000000000000000011 #
b00000000000000000000000000000100 +
13
r0.2 >
1N
#25
03
0N
#30
b00000000000000000000000000000100 #
b00000000000000000000000000000101 +
13
r0.3 >
1N
#35
03
0N
#40
b00000000000000000000000000000101 #
b00000000000000000000000000000110 +
13
r0.4 >
1N
#45
03
0N
#50
b00000000000000000000000000000110 #
b00000000000000000000000000000111 +
13
r0.5 >
1N
#55
03
0N
#60
b00000000000000000000000000000111 #
b00000000000000000000000000001000 +
13
r0.6 >
1N
#65
03
0N
#70
b00000000000000000000000000001000 #
b00000000000000000000000000001001 +
13
r0.7 >
1N
#75
03
0N
#80
b00000000000000000000000000001001 #
b00000000000000000000000000001010 +
13
r0.7999999999999999 >
1N
#85
03
0N
#90
b00000000000000000000000000001010 #
b00000000000000000000000000001011 +
13
r0.8999999999999999 >
1N
#95
03
0N
#100
b00000000000000000000000000001011 #
b00000000000000000000000000001100 +
13
r0.9999999999999999 >
1N
