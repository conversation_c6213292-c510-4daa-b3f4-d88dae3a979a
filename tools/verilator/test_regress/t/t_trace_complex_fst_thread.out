$date
	Wed May  1 19:09:18 2019

$end
$version
	fstWriter
$end
$timescale
	1ns
$end
$scope module top $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var integer 32 " cyc $end
$var logic 2 # v_strp $end
$var logic 4 $ v_strp_strp $end
$var logic 2 % v_unip_strp $end
$var logic 2 & v_arrp $end
$var logic 4 ' v_arrp_arrp $end
$var logic 4 ( v_arrp_strp $end
$var logic 1 ) v_arru(1) $end
$var logic 1 * v_arru(2) $end
$var logic 1 + v_arru_arru(3)(1) $end
$var logic 1 , v_arru_arru(3)(2) $end
$var logic 1 - v_arru_arru(4)(1) $end
$var logic 1 . v_arru_arru(4)(2) $end
$var logic 2 / v_arru_arrp(3) $end
$var logic 2 0 v_arru_arrp(4) $end
$var logic 2 1 v_arru_strp(3) $end
$var logic 2 2 v_arru_strp(4) $end
$var real 64 3 v_real $end
$var real 64 4 v_arr_real(0) $end
$var real 64 5 v_arr_real(1) $end
$var logic 64 6 v_str32x2 $end
$attrbegin misc 07 t.enumed_t 4 ZERO ONE TWO THREE 00000000000000000000000000000000 00000000000000000000000000000001 00000000000000000000000000000010 00000000000000000000000000000011 1 $end
$attrbegin misc 07 "" 1 $end
$var logic 32 7 v_enumed $end
$attrbegin misc 07 "" 1 $end
$var logic 32 8 v_enumed2 $end
$attrbegin misc 07 t.enumb_t 4 BZERO BONE BTWO BTHREE 000 001 010 011 2 $end
$attrbegin misc 07 "" 2 $end
$var logic 3 9 v_enumb $end
$scope module unnamedblk1 $end
$var integer 32 : b $end
$scope module unnamedblk2 $end
$var integer 32 ; a $end
$upscope $end
$upscope $end
$scope module p2 $end
$var parameter 32 < PARAM $end
$upscope $end
$scope module p3 $end
$var parameter 32 = PARAM $end
$upscope $end
$upscope $end
$scope module $unit $end
$var bit 1 > global_bit $end
$upscope $end
$upscope $end
$enddefinitions $end
$dumpvars
0!
b00000000000000000000000000000000 "
b00 #
b0000 $
b00 %
b00 &
b0000 '
b0000 (
0)
0*
0+
0,
0-
0.
b00 /
b00 0
b00 1
b00 2
r0 3
r0 4
r0 5
b0000000000000000000000000000000000000000000000000000000011111111 6
b00000000000000000000000000000000 7
b00000000000000000000000000000000 8
b000 9
b00000000000000000000000000000000 :
b00000000000000000000000000000000 ;
b00000000000000000000000000000010 <
b00000000000000000000000000000011 =
1>
#10
b00000000000000000000000000000101 ;
b00000000000000000000000000000101 :
b111 9
b00000000000000000000000000000010 8
b00000000000000000000000000000001 7
b0000000000000000000000000000000100000000000000000000000011111110 6
r0.3 5
r0.2 4
r0.1 3
b11 2
b11 1
b11 0
b11 /
b1111 (
b1111 '
b11 &
b11 %
b1111 $
b11 #
b00000000000000000000000000000001 "
1!
#15
0!
#20
1!
b00000000000000000000000000000010 "
b00 #
b0000 $
b00 %
b00 &
b0000 '
b0000 (
b00 /
b00 0
b00 1
b00 2
r0.2 3
r0.4 4
r0.6 5
b0000000000000000000000000000001000000000000000000000000011111101 6
b00000000000000000000000000000010 7
b00000000000000000000000000000100 8
b110 9
b00000000000000000000000000000101 :
b00000000000000000000000000000101 ;
#25
0!
#30
1!
b00000000000000000000000000000101 ;
b00000000000000000000000000000101 :
b101 9
b00000000000000000000000000000110 8
b00000000000000000000000000000011 7
b0000000000000000000000000000001100000000000000000000000011111100 6
r0.8999999999999999 5
r0.6000000000000001 4
r0.3 3
b11 2
b11 1
b11 0
b11 /
b1111 (
b1111 '
b11 &
b11 %
b1111 $
b11 #
b00000000000000000000000000000011 "
#35
0!
#40
1!
b00000000000000000000000000000100 "
b00 #
b0000 $
b00 %
b00 &
b0000 '
b0000 (
b00 /
b00 0
b00 1
b00 2
r0.4 3
r0.8 4
r1.2 5
b0000000000000000000000000000010000000000000000000000000011111011 6
b00000000000000000000000000000100 7
b00000000000000000000000000001000 8
b100 9
b00000000000000000000000000000101 :
b00000000000000000000000000000101 ;
#45
0!
#50
1!
b00000000000000000000000000000101 ;
b00000000000000000000000000000101 :
b011 9
b00000000000000000000000000001010 8
b00000000000000000000000000000101 7
b0000000000000000000000000000010100000000000000000000000011111010 6
r1.5 5
r1 4
r0.5 3
b11 2
b11 1
b11 0
b11 /
b1111 (
b1111 '
b11 &
b11 %
b1111 $
b11 #
b00000000000000000000000000000101 "
#55
0!
#60
1!
b00000000000000000000000000000110 "
b00 #
b0000 $
b00 %
b00 &
b0000 '
b0000 (
b00 /
b00 0
b00 1
b00 2
r0.6 3
r1.2 4
r1.8 5
b0000000000000000000000000000011000000000000000000000000011111001 6
b00000000000000000000000000000110 7
b00000000000000000000000000001100 8
b010 9
b00000000000000000000000000000101 :
b00000000000000000000000000000101 ;
