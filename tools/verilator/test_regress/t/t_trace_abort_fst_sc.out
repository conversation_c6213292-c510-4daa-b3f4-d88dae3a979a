$date
	Wed Feb 23 00:00:24 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module top $end
$scope module t $end
$var wire 1 ! clk $end
$var logic 3 " cyc [2:0] $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b000 "
0!
$end
#10
1!
b001 "
#11
#12
#13
#14
#15
0!
#16
#17
#18
#19
#20
1!
b010 "
#21
#22
#23
#24
#25
0!
#26
#27
#28
#29
#30
1!
b011 "
#31
#32
#33
#34
#35
0!
#36
#37
#38
#39
#40
1!
b100 "
#41
#42
#43
#44
#45
0!
#46
#47
#48
#49
#50
1!
b101 "
#51
#52
#53
#54
#55
0!
#56
#57
#58
#59
#60
1!
b110 "
#61
#62
#63
#64
#65
0!
#66
#67
#68
#69
#70
1!
b111 "
#71
#72
#73
#74
#75
0!
#76
#77
#78
#79
