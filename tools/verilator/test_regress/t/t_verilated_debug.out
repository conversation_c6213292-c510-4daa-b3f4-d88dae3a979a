-V{t#,#}- Verilated::debug is on. Message prefix indicates {<thread>,<sequence_number>}.
-V{t#,#}+    Vt_verilated_debug___024root___ctor_var_reset
internalsDump:
  Version: Verilator ###
  Argv: obj_vlt/t_verilated_debug/Vt_verilated_debug
  scopesDump:

-V{t#,#}+++++TOP Evaluate Vt_verilated_debug::eval_step
-V{t#,#}+    Vt_verilated_debug___024root___eval_debug_assertions
-V{t#,#}+    Vt_verilated_debug___024root___eval_initial
-V{t#,#}+    Vt_verilated_debug___024root___initial__TOP__0
  Data: w96: 000000aa 000000bb 000000cc 
-V{t#,#}+ Initial loop
-V{t#,#}+    Vt_verilated_debug___024root___eval_settle
-V{t#,#}+    Vt_verilated_debug___024root___eval
-V{t#,#}+ Clock loop
-V{t#,#}+    Vt_verilated_debug___024root___eval
-V{t#,#}+++++TOP Evaluate Vt_verilated_debug::eval_step
-V{t#,#}+    Vt_verilated_debug___024root___eval_debug_assertions
-V{t#,#}+ Clock loop
-V{t#,#}+    Vt_verilated_debug___024root___eval
-V{t#,#}+    Vt_verilated_debug___024root___sequent__TOP__0
*-* All Finished *-*
-V{t#,#}+    Vt_verilated_debug___024root___final
