:: In top.t
Time scale of t is 1fs / 1fs
[60] time%0d=60  123%0t=123
  dig%0t=5432109876543210 dig%0d=5432109876543210
  rdig%0t=5432109876543210 rdig%0f=5432109876543210.000000
  acc%0t=12345678901234567890 acc%0d=12345678901234567890
[0.000060ns] time%0d=60  123%0t=0.000123ns
  dig%0t=5432109876.543210ns dig%0d=5432109876543210
  rdig%0t=5432109876.543210ns rdig%0f=5432109876543210.000000
  acc%0t=12345678901234.567890ns acc%0d=12345678901234567890
[0.000060ns] stime%0t=0.000060ns  stime%0d=60  stime%0f=60.000000
[0.000060ns] rtime%0t=0.000060ns  rtime%0d=60  rtime%0f=60.000000
global vpiSimTime = 0,60  vpiScaledRealTime = 60
global vpiTimeUnit = -15  vpiTimePrecision = -15
top.t vpiSimTime = 0,60  vpiScaledRealTime = 60
top.t vpiTimeUnit = -15  vpiTimePrecision = -15
*-* All Finished *-*
