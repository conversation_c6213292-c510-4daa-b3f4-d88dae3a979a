$version Generated by VerilatedVcd $end
$date Tue Apr  7 21:19:07 2020
 $end
$timescale   1ms $end

 $scope module top $end
  $var wire  1 + clk $end
  $scope module t $end
   $var wire  1 + clk $end
   $var wire 32 # cyc [31:0] $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000000 #
0+
#10
b00000000000000000000000000000001 #
1+
#15
0+
#20
b00000000000000000000000000000010 #
1+
#25
0+
#30
b00000000000000000000000000000011 #
1+
#35
0+
#40
b00000000000000000000000000000100 #
1+
#45
0+
#50
b00000000000000000000000000000101 #
1+
#55
0+
#60
b00000000000000000000000000000110 #
1+
#65
0+
#70
b00000000000000000000000000000111 #
1+
#75
0+
#80
b00000000000000000000000000001000 #
1+
#85
0+
#90
b00000000000000000000000000001001 #
1+
#95
0+
#100
b00000000000000000000000000001010 #
1+
#105
0+
#110
1+
b00000000000000000000000000001011 #
