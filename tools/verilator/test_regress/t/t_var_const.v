// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2011 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t (/*AUTOARG*/
   // Inputs
   clk
   );

   input clk;

   const logic [2:0] five = 3'd5;

   const logic unsigned [31:0] var_const = 22;
   logic [7:0] res_const;
   assign res_const = var_const[7:0];  // bug693

   always @ (posedge clk) begin
      if (five !== 3'd5) $stop;
      if (res_const !== 8'd22) $stop;
      $write("*-* All Finished *-*\n");
      $finish;
   end

endmodule
