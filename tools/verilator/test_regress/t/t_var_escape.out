$version Generated by VerilatedVcd $end
$date Tue Jul 24 18:44:43 2012
 $end
$timescale 1ps $end

 $scope module top $end
  $var wire  1 * 9num $end
  $var wire  1 + bra[ket]slash/dash-colon:9backslash\done $end
  $var wire  1 ' clk $end
  $var wire  1 ) double__underscore $end
  $var wire  1 ( escaped_normal $end
  $scope module t $end
   $var wire  1 * 9num $end
   $var wire 32 & a0.cyc [31:0] $end
   $var wire  1 + bra[ket]slash/dash-colon:9backslash\done $end
   $var wire  1 $ check:alias $end
   $var wire  1 % check;alias $end
   $var wire  1 $ check_alias $end
   $var wire  1 ' clk $end
   $var wire 32 # cyc [31:0] $end
   $var wire  1 ) double__underscore $end
   $var wire  1 ( escaped_normal $end
   $var wire 32 & other.cyc [31:0] $end
   $var wire  1 $ wire $end
   $scope module a0 $end
    $var wire 32 # cyc [31:0] $end
   $upscope $end
   $scope module mod.with_dot $end
    $var wire 32 # cyc [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1$
0%
b11111111111111111111111111111110 &
b00000000000000000000000000000001 #
0'
1(
1)
1*
1+
#10
0$
1%
b11111111111111111111111111111101 &
b00000000000000000000000000000010 #
1'
0(
0)
0*
0+
#15
0'
#20
1$
0%
b11111111111111111111111111111100 &
b00000000000000000000000000000011 #
1'
1(
1)
1*
1+
#25
0'
#30
0$
1%
b11111111111111111111111111111011 &
b00000000000000000000000000000100 #
1'
0(
0)
0*
0+
#35
0'
#40
1$
0%
b11111111111111111111111111111010 &
b00000000000000000000000000000101 #
1'
1(
1)
1*
1+
#45
0'
#50
0$
1%
b11111111111111111111111111111001 &
b00000000000000000000000000000110 #
1'
0(
0)
0*
0+
#55
0'
#60
1$
0%
b11111111111111111111111111111000 &
b00000000000000000000000000000111 #
1'
1(
1)
1*
1+
#65
0'
#70
0$
1%
b11111111111111111111111111110111 &
b00000000000000000000000000001000 #
1'
0(
0)
0*
0+
#75
0'
#80
1$
0%
b11111111111111111111111111110110 &
b00000000000000000000000000001001 #
1'
1(
1)
1*
1+
#85
0'
#90
0$
1%
b11111111111111111111111111110101 &
b00000000000000000000000000001010 #
1'
0(
0)
0*
0+
#95
0'
#100
1$
0%
b11111111111111111111111111110100 &
b00000000000000000000000000001011 #
1'
1(
1)
1*
1+
