$version Generated by VerilatedVcd $end
$date Sat Mar  6 21:09:47 2021 $end
$timescale 1ps $end

 $scope module top0 $end
  $var wire  1 # clk $end
  $var wire 32 ' counter [31:0] $end
  $var wire  1 ( done_o $end
  $var wire  1 $ rst $end
  $var wire  1 & stop $end
  $var wire 32 % trace_number [31:0] $end
  $scope module top $end
   $var wire  1 # clk $end
   $var wire 32 ' counter [31:0] $end
   $var wire  1 ( done_o $end
   $var wire  1 $ rst $end
   $var wire  1 & stop $end
   $var wire 32 % trace_number [31:0] $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
1$
b00000000000000000000000000000001 %
0&
b00000000000000000000000000000000 '
0(
#1
1#
#2
0#
0$
#3
1#
b00000000000000000000000000000001 '
#4
0#
#5
1#
b00000000000000000000000000000010 '
#6
0#
#7
1#
b00000000000000000000000000000011 '
#8
0#
#9
1#
b00000000000000000000000000000100 '
#10
0#
#11
1#
b00000000000000000000000000000101 '
#12
0#
#13
1#
b00000000000000000000000000000110 '
#14
0#
#15
1#
b00000000000000000000000000000111 '
#16
0#
#17
1#
b00000000000000000000000000001000 '
#18
0#
#19
1#
b00000000000000000000000000001001 '
#20
0#
#21
1#
b00000000000000000000000000001010 '
1(
