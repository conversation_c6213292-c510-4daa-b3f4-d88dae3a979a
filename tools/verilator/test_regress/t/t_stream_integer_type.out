%Warning-WIDTH: t/t_stream_integer_type.v:118:28: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's STREAML generates 8 bits.
                                                : ... In instance t
  118 |          packed_data_32    = {<<8{byte_in}};
      |                            ^
                ... For warning description see https://verilator.org/warn/WIDTH?v=latest
                ... Use "/* verilator lint_off WIDTH */" and lint_on around source to disable this message.
%Warning-WIDTH: t/t_stream_integer_type.v:119:28: Operator ASSIGN expects 64 bits on the Assign RHS, but Assign RHS's STREAML generates 16 bits.
                                                : ... In instance t
  119 |          packed_data_64    = {<<16{shortint_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:120:28: Operator ASSIGN expects 128 bits on the Assign RHS, but Assign RHS's STREAML generates 32 bits.
                                                : ... In instance t
  120 |          packed_data_128   = {<<32{int_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:121:28: Operator ASSIGN expects 128 bits on the Assign RHS, but Assign RHS's STREAML generates 32 bits.
                                                : ... In instance t
  121 |          packed_data_128_i = {<<32{integer_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:122:28: Operator ASSIGN expects 256 bits on the Assign RHS, but Assign RHS's STREAML generates 64 bits.
                                                : ... In instance t
  122 |          packed_data_256   = {<<64{longint_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:123:28: Operator ASSIGN expects 256 bits on the Assign RHS, but Assign RHS's STREAML generates 64 bits.
                                                : ... In instance t
  123 |          packed_time_256   = {<<64{time_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:124:28: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's STREAML generates 8 bits.
                                                : ... In instance t
  124 |          v_packed_data_32  = {<<8{bit_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:125:28: Operator ASSIGN expects 64 bits on the Assign RHS, but Assign RHS's STREAML generates 16 bits.
                                                : ... In instance t
  125 |          v_packed_data_64  = {<<16{logic_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:126:28: Operator ASSIGN expects 128 bits on the Assign RHS, but Assign RHS's STREAML generates 32 bits.
                                                : ... In instance t
  126 |          v_packed_data_128 = {<<32{reg_in}};
      |                            ^
%Warning-WIDTH: t/t_stream_integer_type.v:128:31: Operator ASSIGN expects 8 bits on the Assign RHS, but Assign RHS's VARREF 'packed_data_32' generates 32 bits.
                                                : ... In instance t
  128 |          {<<8{byte_out}}      = packed_data_32;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:129:31: Operator ASSIGN expects 16 bits on the Assign RHS, but Assign RHS's VARREF 'packed_data_64' generates 64 bits.
                                                : ... In instance t
  129 |          {<<16{shortint_out}} = packed_data_64;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:130:31: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's VARREF 'packed_data_128' generates 128 bits.
                                                : ... In instance t
  130 |          {<<32{int_out}}      = packed_data_128;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:131:31: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's VARREF 'packed_data_128_i' generates 128 bits.
                                                : ... In instance t
  131 |          {<<32{integer_out}}  = packed_data_128_i;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:132:31: Operator ASSIGN expects 64 bits on the Assign RHS, but Assign RHS's VARREF 'packed_data_256' generates 256 bits.
                                                : ... In instance t
  132 |          {<<64{longint_out}}  = packed_data_256;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:133:31: Operator ASSIGN expects 64 bits on the Assign RHS, but Assign RHS's VARREF 'packed_time_256' generates 256 bits.
                                                : ... In instance t
  133 |          {<<64{time_out}}     = packed_time_256;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:134:31: Operator ASSIGN expects 8 bits on the Assign RHS, but Assign RHS's VARREF 'v_packed_data_32' generates 32 bits.
                                                : ... In instance t
  134 |          {<<8{bit_out}}       = v_packed_data_32;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:135:31: Operator ASSIGN expects 16 bits on the Assign RHS, but Assign RHS's VARREF 'v_packed_data_64' generates 64 bits.
                                                : ... In instance t
  135 |          {<<16{logic_out}}    = v_packed_data_64;
      |                               ^
%Warning-WIDTH: t/t_stream_integer_type.v:136:31: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's VARREF 'v_packed_data_128' generates 128 bits.
                                                : ... In instance t
  136 |          {<<32{reg_out}}      = v_packed_data_128;
      |                               ^
%Error: t/t_stream_integer_type.v:150:33: Operator STREAML expected non-datatype RHS but 'byte' is a datatype.
                                        : ... In instance t
  150 |          packed_data_32    = {<<byte{byte_in}};
      |                                 ^~~~
%Warning-WIDTH: t/t_stream_integer_type.v:150:28: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's STREAML generates 8 bits.
                                                : ... In instance t
  150 |          packed_data_32    = {<<byte{byte_in}};
      |                            ^
%Error: t/t_stream_integer_type.v:151:33: Operator STREAML expected non-datatype RHS but 'shortint' is a datatype.
                                        : ... In instance t
  151 |          packed_data_64    = {<<shortint{shortint_in}};
      |                                 ^~~~~~~~
%Warning-WIDTH: t/t_stream_integer_type.v:151:28: Operator ASSIGN expects 64 bits on the Assign RHS, but Assign RHS's STREAML generates 16 bits.
                                                : ... In instance t
  151 |          packed_data_64    = {<<shortint{shortint_in}};
      |                            ^
%Error: t/t_stream_integer_type.v:152:33: Operator STREAML expected non-datatype RHS but 'int' is a datatype.
                                        : ... In instance t
  152 |          packed_data_128   = {<<int{int_in}};
      |                                 ^~~
%Warning-WIDTH: t/t_stream_integer_type.v:152:28: Operator ASSIGN expects 128 bits on the Assign RHS, but Assign RHS's STREAML generates 32 bits.
                                                : ... In instance t
  152 |          packed_data_128   = {<<int{int_in}};
      |                            ^
%Error: t/t_stream_integer_type.v:153:33: Operator STREAML expected non-datatype RHS but 'integer' is a datatype.
                                        : ... In instance t
  153 |          packed_data_128_i = {<<integer{integer_in}};
      |                                 ^~~~~~~
%Warning-WIDTH: t/t_stream_integer_type.v:153:28: Operator ASSIGN expects 128 bits on the Assign RHS, but Assign RHS's STREAML generates 32 bits.
                                                : ... In instance t
  153 |          packed_data_128_i = {<<integer{integer_in}};
      |                            ^
%Error: t/t_stream_integer_type.v:154:33: Operator STREAML expected non-datatype RHS but 'longint' is a datatype.
                                        : ... In instance t
  154 |          packed_data_256   = {<<longint{longint_in}};
      |                                 ^~~~~~~
%Warning-WIDTH: t/t_stream_integer_type.v:154:28: Operator ASSIGN expects 256 bits on the Assign RHS, but Assign RHS's STREAML generates 64 bits.
                                                : ... In instance t
  154 |          packed_data_256   = {<<longint{longint_in}};
      |                            ^
%Error: t/t_stream_integer_type.v:155:33: Operator STREAML expected non-datatype RHS but 'time' is a datatype.
                                        : ... In instance t
  155 |          packed_time_256   = {<<time{time_in}};
      |                                 ^~~~
%Warning-WIDTH: t/t_stream_integer_type.v:155:28: Operator ASSIGN expects 256 bits on the Assign RHS, but Assign RHS's STREAML generates 64 bits.
                                                : ... In instance t
  155 |          packed_time_256   = {<<time{time_in}};
      |                            ^
%Error: t/t_stream_integer_type.v:156:33: Operator STREAML expected non-datatype RHS but 'test_byte' is a datatype.
                                        : ... In instance t
  156 |          v_packed_data_32  = {<<test_byte{bit_in}};
      |                                 ^~~~~~~~~
%Error: t/t_stream_integer_type.v:156:31: Slice size isn't a constant or basic data type.
                                        : ... In instance t
  156 |          v_packed_data_32  = {<<test_byte{bit_in}};
      |                               ^~
%Error: Internal Error: t/t_stream_integer_type.v:156:28: ../V3Width.cpp:#: Node has no type
                                                        : ... In instance t
  156 |          v_packed_data_32  = {<<test_byte{bit_in}};
      |                            ^
