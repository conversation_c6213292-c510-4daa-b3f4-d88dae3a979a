$version Generated by VerilatedVcd $end
$date Wed Aug 11 12:41:22 2021 $end
$timescale 1ps $end

 $scope module top $end
  $var wire  1 I clk $end
  $scope module $unit $end
   $var wire  1 # global_bit $end
  $upscope $end
  $scope module t $end
   $var wire  1 S LONGSTART_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_LONGEND $end
   $var wire  1 I clk $end
   $var wire 32 $ cyc [31:0] $end
   $var wire  8 Q unpacked_array[-1] [7:0] $end
   $var wire  8 P unpacked_array[-2] [7:0] $end
   $var wire  8 R unpacked_array[0] [7:0] $end
   $var real 64 < v_arr_real[0] $end
   $var real 64 > v_arr_real[1] $end
   $var wire  2 - v_arrp [2:1] $end
   $var wire  2 . v_arrp_arrp[3] [2:1] $end
   $var wire  2 / v_arrp_arrp[4] [2:1] $end
   $var wire  1 J v_arru[1] $end
   $var wire  1 K v_arru[2] $end
   $var wire  2 4 v_arru_arrp[3] [2:1] $end
   $var wire  2 5 v_arru_arrp[4] [2:1] $end
   $var wire  1 L v_arru_arru[3][1] $end
   $var wire  1 M v_arru_arru[3][2] $end
   $var wire  1 N v_arru_arru[4][1] $end
   $var wire  1 O v_arru_arru[4][2] $end
   $var wire  3 D v_enumb [2:0] $end
   $var wire 32 B v_enumed [31:0] $end
   $var wire 32 C v_enumed2 [31:0] $end
   $var real 64 : v_real $end
   $scope module unnamedblk1 $end
    $var wire 32 G b [31:0] $end
    $scope module unnamedblk2 $end
     $var wire 32 H a [31:0] $end
    $upscope $end
   $upscope $end
   $scope struct v_arrp_strp[3] $end
    $var wire  1 1 b0 $end
    $var wire  1 0 b1 $end
   $upscope $end
   $scope struct v_arrp_strp[4] $end
    $var wire  1 3 b0 $end
    $var wire  1 2 b1 $end
   $upscope $end
   $scope struct v_arru_strp[3] $end
    $var wire  1 7 b0 $end
    $var wire  1 6 b1 $end
   $upscope $end
   $scope struct v_arru_strp[4] $end
    $var wire  1 9 b0 $end
    $var wire  1 8 b1 $end
   $upscope $end
   $scope struct v_enumb2_str $end
    $var wire  3 E a [2:0] $end
    $var wire  3 F b [2:0] $end
   $upscope $end
   $scope struct v_str32x2[0] $end
    $var wire 32 @ data [31:0] $end
   $upscope $end
   $scope struct v_str32x2[1] $end
    $var wire 32 A data [31:0] $end
   $upscope $end
   $scope struct v_strp_strp $end
    $scope struct x0 $end
     $var wire  1 * b0 $end
     $var wire  1 ) b1 $end
    $upscope $end
    $scope struct x1 $end
     $var wire  1 ( b0 $end
     $var wire  1 ' b1 $end
    $upscope $end
   $upscope $end
   $scope struct v_strp $end
    $var wire  1 & b0 $end
    $var wire  1 % b1 $end
   $upscope $end
   $scope union v_unip_strp $end
    $scope struct x0 $end
     $var wire  1 , b0 $end
     $var wire  1 + b1 $end
    $upscope $end
    $scope struct x1 $end
     $var wire  1 , b0 $end
     $var wire  1 + b1 $end
    $upscope $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1#
b00000000000000000000000000000000 $
0%
0&
0'
0(
0)
0*
0+
0,
b00 -
b00 .
b00 /
00
01
02
03
b00 4
b00 5
06
07
08
09
r0 :
r0 <
r0 >
b00000000000000000000000011111111 @
b00000000000000000000000000000000 A
b00000000000000000000000000000000 B
b00000000000000000000000000000000 C
b000 D
b000 E
b000 F
b00000000000000000000000000000000 G
b00000000000000000000000000000000 H
0I
0J
0K
0L
0M
0N
0O
b00000000 P
b00000000 Q
b00000000 R
0S
#10
b00000000000000000000000000000001 $
1%
1&
1'
1(
1)
1*
1+
1,
b11 -
b11 .
b11 /
10
11
12
13
b11 4
b11 5
16
17
18
19
r0.1 :
r0.2 <
r0.3 >
b00000000000000000000000011111110 @
b00000000000000000000000000000001 A
b00000000000000000000000000000001 B
b00000000000000000000000000000010 C
b111 D
b00000000000000000000000000000101 G
b00000000000000000000000000000101 H
1I
#15
0I
#20
b00000000000000000000000000000010 $
0%
0&
0'
0(
0)
0*
0+
0,
b00 -
b00 .
b00 /
00
01
02
03
b00 4
b00 5
06
07
08
09
r0.2 :
r0.4 <
r0.6 >
b00000000000000000000000011111101 @
b00000000000000000000000000000010 A
b00000000000000000000000000000010 B
b00000000000000000000000000000100 C
b110 D
b111 E
b111 F
1I
#25
0I
#30
b00000000000000000000000000000011 $
1%
1&
1'
1(
1)
1*
1+
1,
b11 -
b11 .
b11 /
10
11
12
13
b11 4
b11 5
16
17
18
19
r0.3 :
r0.6000000000000001 <
r0.8999999999999999 >
b00000000000000000000000011111100 @
b00000000000000000000000000000011 A
b00000000000000000000000000000011 B
b00000000000000000000000000000110 C
b101 D
b110 E
b110 F
1I
#35
0I
#40
b00000000000000000000000000000100 $
0%
0&
0'
0(
0)
0*
0+
0,
b00 -
b00 .
b00 /
00
01
02
03
b00 4
b00 5
06
07
08
09
r0.4 :
r0.8 <
r1.2 >
b00000000000000000000000011111011 @
b00000000000000000000000000000100 A
b00000000000000000000000000000100 B
b00000000000000000000000000001000 C
b100 D
b101 E
b101 F
1I
#45
0I
#50
b00000000000000000000000000000101 $
1%
1&
1'
1(
1)
1*
1+
1,
b11 -
b11 .
b11 /
10
11
12
13
b11 4
b11 5
16
17
18
19
r0.5 :
r1 <
r1.5 >
b00000000000000000000000011111010 @
b00000000000000000000000000000101 A
b00000000000000000000000000000101 B
b00000000000000000000000000001010 C
b011 D
b100 E
b100 F
1I
#55
0I
#60
b00000000000000000000000000000110 $
0%
0&
0'
0(
0)
0*
0+
0,
b00 -
b00 .
b00 /
00
01
02
03
b00 4
b00 5
06
07
08
09
r0.6 :
r1.2 <
r1.8 >
b00000000000000000000000011111001 @
b00000000000000000000000000000110 A
b00000000000000000000000000000110 B
b00000000000000000000000000001100 C
b010 D
b011 E
b011 F
1I
