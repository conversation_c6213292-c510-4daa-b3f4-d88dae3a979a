%Warning-UNOPT: t/t_unopt_converge.v:19:11: Signal unoptimizable: Feedback to public clock or circular logic: 'x'
   19 |    output x;    
      |           ^
                ... For warning description see https://verilator.org/warn/UNOPT?v=latest
                ... Use "/* verilator lint_off UNOPT */" and lint_on around source to disable this message.
                t/t_unopt_converge.v:19:11:      Example path: x
                t/t_unopt_converge.v:22:4:      Example path: ALWAYS
                t/t_unopt_converge.v:19:11:      Example path: x
%Error: Exiting due to
