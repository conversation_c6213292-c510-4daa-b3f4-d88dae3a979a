$date
	Wed Feb 23 00:01:11 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module top $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var integer 32 " cyc [31:0] $end
$var logic 2 # v_strp [1:0] $end
$var logic 4 $ v_strp_strp [3:0] $end
$var logic 2 % v_unip_strp [1:0] $end
$var logic 2 & v_arrp [2:1] $end
$var logic 4 ' v_arrp_arrp [3:0] $end
$var logic 4 ( v_arrp_strp [3:0] $end
$var logic 1 ) v_arru[1] $end
$var logic 1 * v_arru[2] $end
$var logic 1 + v_arru_arru[3][1] $end
$var logic 1 , v_arru_arru[3][2] $end
$var logic 1 - v_arru_arru[4][1] $end
$var logic 1 . v_arru_arru[4][2] $end
$var logic 2 / v_arru_arrp[3] [2:1] $end
$var logic 2 0 v_arru_arrp[4] [2:1] $end
$var logic 2 1 v_arru_strp[3] [1:0] $end
$var logic 2 2 v_arru_strp[4] [1:0] $end
$var real 64 3 v_real $end
$var real 64 4 v_arr_real[0] $end
$var real 64 5 v_arr_real[1] $end
$var logic 64 6 v_str32x2 [63:0] $end
$attrbegin misc 07 t.enumed_t 4 ZERO ONE TWO THREE 00000000000000000000000000000000 00000000000000000000000000000001 00000000000000000000000000000010 00000000000000000000000000000011 1 $end
$attrbegin misc 07 "" 1 $end
$var logic 32 7 v_enumed [31:0] $end
$attrbegin misc 07 "" 1 $end
$var logic 32 8 v_enumed2 [31:0] $end
$attrbegin misc 07 t.enumb_t 4 BZERO BONE BTWO BTHREE 000 001 010 011 2 $end
$attrbegin misc 07 "" 2 $end
$var logic 3 9 v_enumb [2:0] $end
$var logic 6 : v_enumb2_str [5:0] $end
$var logic 8 ; unpacked_array[-2] [7:0] $end
$var logic 8 < unpacked_array[-1] [7:0] $end
$var logic 8 = unpacked_array[0] [7:0] $end
$var bit 1 > LONGSTART_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_LONGEND $end
$scope module a_module_instantiation_with_a_very_long_name_that_once_its_signals_get_concatenated_and_inlined_will_almost_certainly_result_in_them_getting_hashed $end
$var parameter 32 ? PARAM [31:0] $end
$upscope $end
$scope module p2 $end
$var parameter 32 @ PARAM [31:0] $end
$upscope $end
$scope module p3 $end
$var parameter 32 A PARAM [31:0] $end
$upscope $end
$scope module unnamedblk1 $end
$var integer 32 B b [31:0] $end
$scope module unnamedblk2 $end
$var integer 32 C a [31:0] $end
$upscope $end
$upscope $end
$upscope $end
$scope module $unit $end
$var bit 1 D global_bit $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
1D
b00000000000000000000000000000000 C
b00000000000000000000000000000000 B
b00000000000000000000000000000011 A
b00000000000000000000000000000010 @
b00000000000000000000000000000100 ?
0>
b00000000 =
b00000000 <
b00000000 ;
b000000 :
b000 9
b00000000000000000000000000000000 8
b00000000000000000000000000000000 7
b0000000000000000000000000000000000000000000000000000000011111111 6
r0 5
r0 4
r0 3
b00 2
b00 1
b00 0
b00 /
0.
0-
0,
0+
0*
0)
b0000 (
b0000 '
b00 &
b00 %
b0000 $
b00 #
b00000000000000000000000000000000 "
0!
$end
#10
1!
b00000000000000000000000000000001 "
b11 #
b1111 $
b11 %
b11 &
b1111 '
b1111 (
b11 /
b11 0
b11 1
b11 2
r0.1 3
r0.2 4
r0.3 5
b0000000000000000000000000000000100000000000000000000000011111110 6
b00000000000000000000000000000001 7
b00000000000000000000000000000010 8
b111 9
b00000000000000000000000000000101 B
b00000000000000000000000000000101 C
#15
0!
#20
1!
b110 9
b00000000000000000000000000000100 8
b00000000000000000000000000000010 7
b0000000000000000000000000000001000000000000000000000000011111101 6
r0.6 5
r0.4 4
r0.2 3
b00 2
b00 1
b00 0
b00 /
b0000 (
b0000 '
b00 &
b00 %
b0000 $
b00 #
b00000000000000000000000000000010 "
b111111 :
#25
0!
#30
1!
b110110 :
b00000000000000000000000000000011 "
b11 #
b1111 $
b11 %
b11 &
b1111 '
b1111 (
b11 /
b11 0
b11 1
b11 2
r0.3 3
r0.6000000000000001 4
r0.8999999999999999 5
b0000000000000000000000000000001100000000000000000000000011111100 6
b00000000000000000000000000000011 7
b00000000000000000000000000000110 8
b101 9
#35
0!
#40
1!
b100 9
b00000000000000000000000000001000 8
b00000000000000000000000000000100 7
b0000000000000000000000000000010000000000000000000000000011111011 6
r1.2 5
r0.8 4
r0.4 3
b00 2
b00 1
b00 0
b00 /
b0000 (
b0000 '
b00 &
b00 %
b0000 $
b00 #
b00000000000000000000000000000100 "
b101101 :
#45
0!
#50
1!
b100100 :
b00000000000000000000000000000101 "
b11 #
b1111 $
b11 %
b11 &
b1111 '
b1111 (
b11 /
b11 0
b11 1
b11 2
r0.5 3
r1 4
r1.5 5
b0000000000000000000000000000010100000000000000000000000011111010 6
b00000000000000000000000000000101 7
b00000000000000000000000000001010 8
b011 9
#55
0!
#60
1!
b010 9
b00000000000000000000000000001100 8
b00000000000000000000000000000110 7
b0000000000000000000000000000011000000000000000000000000011111001 6
r1.8 5
r1.2 4
r0.6 3
b00 2
b00 1
b00 0
b00 /
b0000 (
b0000 '
b00 &
b00 %
b0000 $
b00 #
b00000000000000000000000000000110 "
b011011 :
