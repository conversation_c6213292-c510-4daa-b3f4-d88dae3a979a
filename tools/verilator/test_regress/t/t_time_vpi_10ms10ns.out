:: In top.t
Time scale of t is 10ms / 10ns
[60000000] time%0d=60  123%0t=123000000
  dig%0t=543000000 dig%0d=543
  rdig%0t=543210988 rdig%0f=543.210988
  acc%0t=12345678901234567890000000 acc%0d=12345678901234567890
[600000000.000000ns] time%0d=60  123%0t=1230000000.000000ns
  dig%0t=5430000000.000000ns dig%0d=543
  rdig%0t=5432109876.543210ns rdig%0f=543.210988
  acc%0t=123456789012345678900000000.000000ns acc%0d=12345678901234567890
[600000000.000000ns] stime%0t=600000000.000000ns  stime%0d=60  stime%0f=60.000000
[600000000.000000ns] rtime%0t=600000000.000000ns  rtime%0d=60  rtime%0f=60.000000
global vpiSimTime = 0,60000000  vpiScaledRealTime = 6e+07
global vpiTimeUnit = -2  vpiTimePrecision = -8
top.t vpiSimTime = 0,60000000  vpiScaledRealTime = 60
top.t vpiTimeUnit = -2  vpiTimePrecision = -8
*-* All Finished *-*
