%Error: t/t_var_dup2_bad.v:13:9: Duplicate declaration of signal: 'bad_o_w'
                               : ... note: ANSI ports must have type declared with the I/O (IEEE 1800-2017 23.2.2.2)
   13 |    wire bad_o_w;
      |         ^~~~~~~
        t/t_var_dup2_bad.v:10:11: ... Location of original declaration
   10 |    output bad_o_w,
      |           ^~~~~~~
%Error: t/t_var_dup2_bad.v:14:9: Duplicate declaration of signal: 'bad_o_r'
   14 |    reg  bad_o_r;
      |         ^~~~~~~
        t/t_var_dup2_bad.v:11:11: ... Location of original declaration
   11 |    output bad_o_r);
      |           ^~~~~~~
%Error: Exiting due to
