// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed into the Public Domain, for any use,
// without warranty, 2019 by <PERSON><PERSON> Ha<PERSON>di.
// SPDX-License-Identifier: CC0-1.0

`begin_keywords "VAMS-2.3"

module t (/*AUTOARG*/);

   // Just get errors on bad keywords (for code coverage)
   int above;
   int abs;
   int absdelay;
   int abstol;
   int ac_stim;
   int access;
   int acos;
   int acosh;
   int aliasparam;
   int analog;
   int analysis;
   int assert;
   int branch;
   int connect;
   int connectmodule;
   int connectrules;
   int continuous;
   int cross;
   int ddt;
   int ddt_nature;
   int ddx;
   int discipline;
   int discrete;
   int domain;
   int driver_update;
   int endconnectrules;
   int enddiscipline;
   int endnature;
   int endparamset;
   int exclude;
   int final_step;
   int flicker_noise;
   int flow;
   int from;
   int ground;
   int idt;
   int idt_nature;
   int idtmod;
   int inf;
   int initial_step;
   int laplace_nd;
   int laplace_np;
   int laplace_zd;
   int laplace_zp;
   int last_crossing;
   int limexp;
   int max;
   int merged;
   int min;
   int nature;
   int net_resolution;
   int noise_table;
   int paramset;
   int potential;
   int resolveto;
   int slew;
   int split;
   int timer;
   int transition;
   int units;
   int white_noise;
   int zi_nd;
   int zi_np;
   int zi_zd;
   int zi_zp;

endmodule
