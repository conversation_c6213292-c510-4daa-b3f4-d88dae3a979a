$version Generated by VerilatedVcd $end
$date Tue Aug 10 15:49:51 2021 $end
$timescale 1ps $end

 $scope module top $end
  $var wire  1 5 CLK $end
  $var wire  1 6 RESET $end
  $scope module t $end
   $var wire  1 5 CLK $end
   $var wire  1 # RESET $end
   $var wire 32 & val [31:0] $end
   $var wire  2 $ vec[3] [2:1] $end
   $var wire  2 % vec[4] [2:1] $end
   $scope module glbl $end
    $var wire  1 7 GSR $end
   $upscope $end
   $scope module little $end
    $var wire  1 5 clk $end
    $var wire 128 1 i128 [63:190] $end
    $var wire 49 / i48 [1:49] $end
    $var wire  8 . i8 [0:7] $end
   $upscope $end
   $scope module neg $end
    $var wire  1 5 clk $end
    $var wire 128 * i128 [63:-64] $end
    $var wire 48 ( i48 [-1:-48] $end
    $var wire  8 ' i8 [0:-7] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1#
b00 $
b00 %
b00000000000000000000000000000000 &
b00000000 '
b000000000000000000000000000000000000000000000000 (
b00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 *
b00000000 .
b0000000000000000000000000000000000000000000000000 /
b00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 1
05
16
17
#1
#2
#3
b11111111 '
b111111111111111111111111111111111111111111111111 (
b11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111 *
b11111111 .
b1111111111111111111111111111111111111111111111111 /
b11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111 1
15
#4
#5
#6
05
#7
07
#8
#9
0#
b00000000 '
b000000000000000000000000000000000000000000000000 (
b00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 *
b00000000 .
b0000000000000000000000000000000000000000000000000 /
b00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 1
15
06
#10
#11
#12
05
#13
#14
#15
b00000000000000000000000000000001 &
b11111111 '
b111111111111111111111111111111111111111111111111 (
b11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111 *
b11111111 .
b1111111111111111111111111111111111111111111111111 /
b11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111 1
15
#16
#17
#18
05
#19
#20
