######################################################################
#
# DESCRIPTION: CMake version configuration file for Verilator
#
# This allows specifying a minimum Verilator version.
# Include it in your CMakeLists.txt using:
#
#     find_package(verilate 4.0)
#
# Copyright 2003-2022 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
#
######################################################################

set(PACKAGE_VERSION "@PACKAGE_VERSION_NUMBER@")

if(PACKAGE_VERSION VERSION_LESS PACKAGE_FIND_VERSION)
  set(PACKAGE_VERSION_COMPATIBLE FALSE)
else()
  set(PACKAGE_VERSION_COMPATIBLE TRUE)
  if(PACKAGE_FIND_VERSION STREQUAL PACKAGE_VERSION)
    set(PACKAGE_VERSION_EXACT TRUE)
  endif()
endif()
